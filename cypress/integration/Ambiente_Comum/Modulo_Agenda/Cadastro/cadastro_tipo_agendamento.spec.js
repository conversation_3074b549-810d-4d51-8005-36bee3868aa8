describe('Tipo de agendamento ', function() {

    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true)
        cy.visit('novotreino/pt/cadastros/tipo-agendamento')
    })

    it('Cadastro de tipo de agendamento duração predefinida ', function(){   
        cy.get('#btn-novo-tipo-agendamento').click()
        cy.get('#nome-tipo-agendamento-input').type('Tipo Agendamento ' + Math.floor(Math.random() * 100 + 1))
        cy.get('#select-comportamento').select('AVALIACAO_FISICA')
        cy.get('#btn-selecione-cor').click()
        cy.get('#cor-0').click()
        cy.get('#select-tipo-duracao').select('DURACAO_PREDEFINIDA')
        cy.get('#input-duracao-pre-definida').type('01:00')
        cy.get('#status-tipo-agendamento').check('on')
        cy.get('#btn-add-tipo-agendamento').click({force:true})
        cy.contains('Tipo agendamento criado com sucesso.')  
    })

    it('Cadastro de tipo de agendamento duração livre ', function(){   
        cy.get('#btn-novo-tipo-agendamento').click()
        cy.get('#nome-tipo-agendamento-input').type('Tipo Agendamento ' + Math.floor(Math.random() * 100 + 1))
        cy.get('#select-comportamento').select('AVALIACAO_FISICA')
        cy.get('#btn-selecione-cor').click()
        cy.get('#cor-0').click()
        cy.get('#status-tipo-agendamento').check('on')
        cy.get('#btn-add-tipo-agendamento').click()
        cy.contains('Tipo agendamento criado com sucesso.')  
    })

    it('Cadastro de tipo de agendamento sem cor e nome ', function(){   
        cy.get('#btn-novo-tipo-agendamento').click()
        cy.get('#btn-add-tipo-agendamento').click()
        cy.contains('Selecione uma cor.')
        cy.get('#btn-selecione-cor').click()
        cy.get('#cor-0').click()
        cy.get('#status-tipo-agendamento').check('on')
        cy.get('#btn-add-tipo-agendamento').click()
        cy.contains('Campos obrigatórios não preenchido.')
        cy.get('#nome-tipo-agendamento-input').type('Tipo Agendamento ' + Math.floor(Math.random() * 100 + 1))
        cy.get('#btn-add-tipo-agendamento').click()
        cy.contains('Tipo agendamento criado com sucesso.')  
    }) 
})
