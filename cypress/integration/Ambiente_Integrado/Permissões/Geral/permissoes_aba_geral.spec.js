describe('Perfil de acesso ', function() {
    
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true,'p.geral','12345',)
    })
    // Este este foi criado para validar a permissão "Pefil de Acesso", como  a permissão do usuário Pacto não deve ser alterada,
    //vamos usar um segundo usuário para testar a permissão. No teste o pacto vai alterar o perfil de acesso desse segundo usuário.
    // Nos outros testes de permissão e usado o usuário "permi", como ess testes não deve influenciar os outros vamos utilizar o "p.geral"
    it('Valida permissão de perfil de acesso)', function() {
        cy.get('#inputGlobalSearch').type('permi')
        cy.get('#global-search-perfil-acesso').click()
        cy.get('#element-0-remove').should('be.visible')
        cy.get('#element-0-edit').should('be.visible')
        cy.get('#adicionar-novo_perfil').should('be.visible')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('PERMISSAO ABA GERAL')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-geral').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-perfil_usuario').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.get('pacto-plataforma-layout > pacto-plataform-layout-top > .header-container > .header-center > .pct-logo').click()
        cy.reload(true)
        cy.get('#inputGlobalSearch').type('permi')
        cy.get('#global-search-perfil-acesso').click()
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.realizaLogoff()
        cy.get('#fmLay\\3AusernameLoginZW').type('PACTOBR')
        cy.get('#fmLay\\3ApwdLoginZW').type('JSP)34X&*')
        cy.get('#fmLay\\3A btnEntrar').click()
        cy.get('#fmLay\\3A empresa').select('1')
        cy.get('#loginModuloTreinoNovo').should('be.visible')
        cy.get('#loginModuloTreinoNovo').click()
        cy.get('#inputGlobalSearch').type('permi')
        cy.get('#global-search-perfil-acesso').click()
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('PERMISSAO ABA GERAL')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-geral').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-perfil_usuario').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })    
})

describe ('Pemrmissão de usuários', function(){

    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true,'permi','12345')
    })

    it('Valida permissão de usuarrios', function(){
        cy.get('#open-user-drop-menu').click()
        cy.get('#editar-usuario').should('not.be.visible')
        cy.get('#inputGlobalSearch').type('perfil acesso')
        cy.get('#global-search-perfil-acesso').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-geral').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-usuarios').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.get('#open-user-drop-menu').click()
        cy.get('#editar-usuario').click()
        cy.contains('Gerencie os usuários cadastrados')
        cy.get('#element-0-edit').should('be.visible')
        cy.get('#inputGlobalSearch').type('perfil acesso')
        cy.get('#global-search-perfil-acesso').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-geral').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-usuarios').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })
})