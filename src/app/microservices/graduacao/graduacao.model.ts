import { GraduacaoAvaliacaoLivre } from "src/app/microservices/graduacao/avaliacao-livre/avaliacao-livre.model";
import { GraduacaoAvaliacaoProgresso } from "src/app/microservices/graduacao/avaliacao-progresso/avaliacao.model";

export enum GraduacaoAvaliacaoTipo {
	LIVRE = "LIVRE",
	PROGRESSO = "PROGRESSO",
}

export type GraduacaoAvaliacao =
	| GraduacaoAvaliacaoProgresso
	| GraduacaoAvaliacaoLivre;

export interface AvaliacaoAlunoResposta {
	id: number;
	atividade: {
		id: number;
		imageUri: string;
		nome: string;
		descricao: string;
	};
	subAtividade?: {
		id: number;
		atividadeId: number;
		nome: string;
	};
	resposta: number;
	observacao: string;
}

export interface ConsultaRespostasAvaliacaoGrupo {
	observacaoGeral: string;
	respostas: Array<RespostaAvaliacaoGrupo>;
}

export interface RespostaAvaliacaoGrupo {
	respostaAvaliacaoId: number;
	avaliacaoAlunoId: number;
	atividadeId: number;
	subAtividadeId?: number;
	alunoId: number;
	resposta: number;
	observacao: string;
}
