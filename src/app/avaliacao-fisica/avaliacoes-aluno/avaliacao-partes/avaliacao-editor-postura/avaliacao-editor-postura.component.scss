.avaliacao-editor-postura-wrapper {
	padding: 20px;
	background-color: #fff;
}

.image-selector-wrapper {
	display: flex;
	justify-content: space-between;
	margin-bottom: 30px;

	.seletor-container {
		flex-grow: 1;
		flex-shrink: 0;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.title {
		text-align: center;
		font-size: 18px;
		font-weight: 600;
		line-height: 38px;
	}
}

.postura-title {
	font-weight: 600;
	font-size: 17px;
	color: #777;
	border-bottom: 1px solid rgb(170, 170, 170);
	margin-bottom: 10px;
	margin-top: 20px;
	padding-bottom: 5px;
}

.avaliacao-editor-postura-wrapper {
	.input-group-wrapper {
		display: flex;
		flex-wrap: wrap;

		.form-check {
			flex-shrink: 0;
			flex-basis: 33%;
		}
	}
}

@media (max-width: 1360px) {
	.avaliacao-editor-postura-wrapper .input-group-wrapper .form-check {
		flex-basis: 50%;
	}
}
