@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	background-color: $branco;
	border-radius: 10px;

	&.loading {
		filter: blur(2px);
	}
}

.title-section {
	line-height: 74px;
	@extend .type-h5;
	color: $pretoPri;
	border-bottom: 1px solid $canetaBicPastel;
	padding-left: 30px;
}

.days-row {
	margin: 0px 0px 20px 70px;
	justify-content: space-around;
	display: flex;
}

.day {
	text-transform: capitalize;
	text-align: center;

	&.current {
		> * {
			color: $azulimPri;
		}
	}

	.dia-semana-label {
		@extend .type-h6-bold;
	}

	.dia-semana-total {
		@extend .type-caption;
	}
}

.grid-hour-row {
	display: flex;

	.hour-label {
		line-height: 64px;
		text-align: center;
		width: 45px;
		margin-right: 30px;
		@extend .type-p-small;
		color: $cinza03;

		.current {
			color: $preto05;
		}
	}

	.grid-cell {
		flex-basis: 14.2%;
		text-align: center;
		line-height: 64px;
		@extend .type-h6;
		border-bottom: 1px solid $canetaBicPastel;
		border-right: 1px solid $canetaBicPastel;
		cursor: pointer;

		&:last-child {
			border-right: 0px;
		}

		&.current-day {
			background-color: $azulimPastel;
			border-bottom: 0px;

			&.current-hour {
				background-color: $azulim01;
				color: $preto05;
			}
		}
	}
}

.body-section {
	padding: 30px;
}
