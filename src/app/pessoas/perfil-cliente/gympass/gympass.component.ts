import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	On<PERSON>nit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { Api } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";
import {
	AdmCoreApiClienteService,
	AdmCoreApiGympassService,
	ClienteDadosPessoais,
	ClienteDadosPlano,
} from "adm-core-api";
import { SnotifyService } from "ng-snotify";
import { Observable, of, Subscription } from "rxjs";
import { catchError, tap } from "rxjs/operators";
import {
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";

declare var moment;

@Component({
	selector: "pacto-gympass",
	templateUrl: "./gympass.component.html",
	styleUrls: ["./gympass.component.scss"],
})
export class GympassComponent implements OnInit, OnD<PERSON>roy {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("columnDate", { static: true }) columnDate: TemplateRef<any>;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnToken", { static: true }) columnToken: TemplateRef<any>;
	@ViewChild("columnLegenda", { static: true }) columnLegenda: TemplateRef<any>;
	@ViewChild("columnValor", { static: true }) columnValor: TemplateRef<any>;
	@ViewChild("filterStatus", { static: true }) filterStatus: TemplateRef<any>;
	@ViewChild("filterDate", { static: true }) filterDate: TemplateRef<any>;
	@ViewChild("celulaSituacao", { static: true })
	celulaSituacao: TemplateRef<any>;
	@ViewChild("situacaoTranslator", { static: true }) situacaoTranslator;
	@ViewChild("traducao", { static: true })
	traducao: TraducoesXinglingComponent;

	table: PactoDataGridConfig;
	matricula: string;
	filterConfig: any = {};

	tokenControl: FormControl = new FormControl();
	dadosPessoais$: Observable<ClienteDadosPessoais | null>;
	dadosPlano$: Observable<ClienteDadosPlano | null>;
	token$: Observable<any>;

	private subs = new Subscription();

	constructor(
		private route: ActivatedRoute,
		private msAdmCoreService: AdmCoreApiClienteService,
		private restService: RestService,
		private gympassService: AdmCoreApiGympassService,
		private snotify: SnotifyService,
		private router: Router
	) {}

	ngOnInit() {
		this.initTable();
		this.configFilters();

		this.matricula =
			this.route.snapshot.params["aluno-matricula"] ||
			sessionStorage.getItem("pacto-aluno-matricula");

		if (!this.matricula) {
			this.router.navigate(["/cadastros", "alunos", "listagem"]);
		}

		if (this.matricula) {
			this.dadosPessoais$ = this.msAdmCoreService
				.dadosPessoais(this.matricula)
				.pipe(
					tap((dados: ClienteDadosPessoais) => {
						if (!dados.matricula) {
							this.handleInvalidUserError();
							return;
						}
						this.gympassService.getToken(dados.matricula).subscribe((resp) => {
							const token = resp.content.gymPassUniqueToken;

							if (token) {
								this.tokenControl.patchValue(token);
							}
						});

						this.table.endpointUrl = this.restService.buildFullUrl(
							`clientes/${dados.codigoPessoa}/historico-acessos-gympass`,
							null,
							Api.MSADMCORE
						);

						this.tableData.reloadData();
					}),
					catchError(() => {
						this.handleInvalidUserError();

						// prevent empty user info to be displayed on the screen
						return of(null);
					})
				);
			this.dadosPlano$ = this.msAdmCoreService.dadosPlano(this.matricula);
		}
	}

	ngOnDestroy(): void {
		this.subs.unsubscribe();
	}

	initTable(): void {
		this.table = new PactoDataGridConfig({
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 5,
			columns: [
				{
					nome: "dataAcesso",
					titulo: this.columnDate,
					visible: true,
					ordenavel: true,
					width: "20%",
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
					orderColumn: "dataInicio",
				},
				{
					nome: "codigo",
					titulo: this.columnCodigo,
					visible: true,
					ordenavel: true,
					width: "20%",
					styleClass: "center",
				},
				{
					nome: "token",
					titulo: this.columnToken,
					visible: true,
					ordenavel: true,
					width: "40%",
					styleClass: "center",
				},
				{
					nome: "legenda",
					titulo: this.columnLegenda,
					visible: true,
					ordenavel: true,
					width: "10%",
					celula: this.celulaSituacao,
					styleClass: "right",
				},
			],
		});
	}

	saveToken(): void {
		const token = this.tokenControl.value;
		this.subs.add(
			this.gympassService.salvarToken(this.matricula, token).subscribe({
				next: (resp) => {
					if (!token) {
						this.snotify.success(
							this.traducao.getLabel("token-deleted-sucessfully-msg")
						);
					} else {
						this.snotify.success(this.traducao.getLabel("token-success-msg"));
					}
					this.tableData.reloadData();
				},
				error: (e) => {
					this.snotify.error(this.traducao.getLabel("token-error-msg"));
				},
			})
		);
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "dataAcesso",
					label: this.filterDate,
					type: GridFilterType.DATE_POINT_CAT,
				},
			],
		};
	}

	private handleInvalidUserError(): void {
		this.snotify.error(this.traducao.getLabel("aluno-invalido"), {
			timeout: 3000,
		});

		setTimeout(() => {
			this.snotify.info(this.traducao.getLabel("redirect-msg"), {
				timeout: 3000,
			});
		}, 3000);

		setTimeout(() => {
			this.router.navigate(["/cadastros", "alunos", "listagem"]);
		}, 6500);
	}
}
