import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import {
	AdmCoreApiAtestadoService,
	AdmCoreApiContratoService,
	AdmCoreApiAtestadoMedicoService,
} from "adm-core-api";
import { SnotifyService } from "ng-snotify";
import { AlunoBase } from "treino-api";
import { AdmLegadoAutorizarAcessoService } from "adm-legado-api";
import { UploadArquivo } from "../classes/upload-arquivo.model";
import { TipoUploadEnum } from "../classes/tipo-upload.enum";
import { ModalDetalharDocumentoComponent } from "../modal-detalhar-documento/modal-detalhar-documento.component";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";

declare var moment;

@Component({
	selector: "pacto-atestado-medico-table",
	templateUrl: "./atestado-medico-table.component.html",
	styleUrls: ["./atestado-medico-table.component.scss"],
})
export class AtestadoMedicoTableComponent implements OnInit {
	@ViewChild("traducao", { static: true })
	traducao: TraducoesXinglingComponent;
	@ViewChild("columnAtestado", { static: true })
	columnAtestado: TemplateRef<any>;
	@ViewChild("columnContrato", { static: true })
	columnContrato: TemplateRef<any>;
	@ViewChild("columnData", { static: true }) columnData: TemplateRef<any>;
	@ViewChild("columnVigencia", { static: true })
	columnVigencia: TemplateRef<any>;
	@ViewChild("relatorioComponent", { static: false })
	relatorioComponent: RelatorioComponent;
	@Input() alunoBase: AlunoBase;
	@Output() atestadosMedicosChangeEvent: EventEmitter<any> = new EventEmitter();
	@Output() editEvent: EventEmitter<any> = new EventEmitter();
	tableDataConfig: PactoDataGridConfig;
	@Input() isPermissaoExcluir: any;
	alunoTemDados: boolean = false;
	@ViewChild("tipoAtestadoCell", { static: true }) tipoAtestadoCell;

	constructor(
		private readonly rest: RestService,
		private notify: SnotifyService,
		private admCoreApiContratoService: AdmCoreApiContratoService,
		private admCoreApiAtestadoService: AdmCoreApiAtestadoService,
		private admCoreApiAtestadoMedicoService: AdmCoreApiAtestadoMedicoService,
		private readonly autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private dialogService: ModalService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.initDados();
		this.initTable();
	}

	initTable(): void {
		this.tableDataConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlAdmCore(
				`atestados-medicos/${this.alunoBase.matricula}`
			),
			quickSearch: false,
			exportButton: false,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			columns: [
				{
					nome: "nomeArquivo",
					titulo: this.columnAtestado,
					visible: true,
					ordenavel: false,
					valueTransform(v) {
						return v || "-";
					},
				},
				{
					nome: "codigoContrato",
					titulo: this.columnContrato,
					visible: true,
					ordenavel: false,
					valueTransform(v) {
						return v || "-";
					},
				},
				{
					nome: "atestadoContrato.responsavelOperacao.nome",
					titulo: "Responsável Operação",
					visible: true,
					ordenavel: false,
					valueTransform(v, row) {
						if (
							row &&
							row.atestadoContrato &&
							row.atestadoContrato.responsavelOperacao !== null &&
							row.atestadoContrato.responsavelOperacao !== undefined &&
							row.atestadoContrato.responsavelOperacao.nome
						) {
							return row.atestadoContrato.responsavelOperacao.nome;
						}

						return "-";
					},
				},
				{
					nome: "dataLancamento",
					titulo: this.columnData,
					visible: true,
					ordenavel: false,
					valueTransform(v) {
						return v != null ? moment(v).format("DD/MM/YYYY - HH:mm:ss") : "-";
					},
					date: true,
				},
				{
					nome: "vigenciaPorExtenso",
					titulo: this.columnVigencia,
					visible: true,
					ordenavel: false,
				},
			],
			actions: [
				{
					nome: "action-visualizar-atestado",
					iconClass: "pct pct-eye cor-azulim05",
					tooltipText: "Visualizar arquivo",
					showIconFn: (row) => row.urlArquivo,
				},
				{
					nome: "action-download-atestado",
					iconClass: "pct pct-download cor-azulim05",
					tooltipText: "Baixar arquivo",
					showIconFn: (row) => row.urlArquivo,
				},
				{
					nome: "action-edit-atestado",
					iconClass: "pct pct-edit cor-azulim05",
					tooltipText: "Editar",
					showIconFn: (row) => row.tipoAtestado === 1,
				},
				{
					nome: "action-delet-atestado",
					iconClass: "pct pct-trash-2 cor-laranjinha05",
					tooltipText: "Excluir",
				},
			],
		});
	}

	initDados() {
		this.admCoreApiAtestadoMedicoService
			.findAllByMatricula(this.alunoBase.matricula)
			.subscribe((response) => {
				this.alunoTemDados = response.length > 0;
				this.cd.detectChanges();
			});
	}

	loadData() {
		this.initDados();
		this.relatorioComponent.reloadData();
	}

	download(row) {
		if (!this.isNullOrEmpty(row.urlArquivo)) {
			window.open(row.urlArquivo, "_blank");
		} else {
			this.notify.warning("Esse registro não possui anexo!");
		}
	}

	isAbrirDocumento(formatoArquivo) {
		return (
			formatoArquivo === ".pdf" ||
			formatoArquivo === ".doc" ||
			formatoArquivo === ".docx" ||
			formatoArquivo === ".txt"
		);
	}

	visualizar(row) {
		console.log(row);
		if (!this.isNullOrEmpty(row.urlArquivo)) {
			if (this.isAbrirDocumento(row.formatoArquivo)) {
				const urlAbrir = row.anexo || row.urlArquivo;
				window.open(urlAbrir, "_blank");
			} else {
				const modalRef = this.dialogService.open(
					"Visualizar Documento",
					ModalDetalharDocumentoComponent,
					PactoModalSize.LARGE
				);
				modalRef.componentInstance.rowData = row;
			}
		} else {
			this.notify.warning("Esse registro não possui anexo!");
		}
	}

	delete(row) {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.isPermissaoExcluir.chave,
				this.isPermissaoExcluir.codigoUsuario,
				this.isPermissaoExcluir.codigoEmpresa,
				this.isPermissaoExcluir.funcionalidade,
				this.isPermissaoExcluir.permissao
			)
			.subscribe(
				(data) => {
					if (row.tipoAtestado === 1) {
						this.admCoreApiAtestadoService
							.excluirAtestadoAptidaoFisica(row.atestadoAptidaoFisica.codigo)
							.subscribe(
								() => {
									this.notify.success("Excluido com sucesso!");
									this.loadData();
								},
								(error) => {
									this.notify.warning("Falha ao tentar excluir!");
								}
							);
					} else if (row.tipoAtestado === 2) {
						if (row.codigoContrato && row.atestadoContrato.contratoOperacao) {
							this.admCoreApiContratoService
								.estornarContratoOperacao(
									row.atestadoContrato.contratoOperacao.codigo
								)
								.subscribe(
									() => {
										this.notify.success("Estornado com sucesso!");
										this.loadData();
									},
									(error) => {
										this.notify.warning("Falha ao tentar estornar!");
									}
								);
						} else {
							this.notify.warning("Falha ao excluir atestado!");
						}
					}
				},
				(error) => {
					this.notify.error(error.error.meta.message);
				}
			);
	}

	edit(row) {
		const uploadArquivo = new UploadArquivo();
		uploadArquivo.nomeArquivo = row.nomeArquivo;
		uploadArquivo.nomeArquivoApresentar = row.nomeArquivo + row.formatoArquivo;
		uploadArquivo.formatoArquivo = row.formatoArquivo;
		uploadArquivo.urlArquivo = row.urlArquivo;
		uploadArquivo.tituloModal = "Atestado Médico";
		if (row.codigoContrato && row.atestadoContrato.contratoOperacao) {
			uploadArquivo.tipo = TipoUploadEnum.ATESTADO_CONTRATO;
			uploadArquivo.observacao =
				row.atestadoContrato.contratoOperacao.observacao;
			uploadArquivo.atestadoContrato = row.atestadoContrato;
		} else if (row.atestadoAptidaoFisica) {
			uploadArquivo.tipo = TipoUploadEnum.ATESTADO_APTIDAO_FISICA;
			uploadArquivo.observacao = row.atestadoAptidaoFisica.observacao;
			uploadArquivo.atestadoAptidaoFisica = row.atestadoAptidaoFisica;
		}
		this.editEvent.emit(uploadArquivo);
	}

	isNullOrEmpty(value) {
		return value === null || value === undefined || value === "";
	}

	iconClickFn(event: { row: any; iconName: string }) {
		switch (event.iconName) {
			case "action-visualizar-atestado":
				this.visualizar(event.row);
				break;
			case "action-download-atestado":
				this.download(event.row);
				break;
			case "action-edit-atestado":
				this.edit(event.row);
				break;
			case "action-delet-atestado":
				this.delete(event.row);
				break;
		}
	}
}
