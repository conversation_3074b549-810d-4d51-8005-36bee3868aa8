<pacto-relatorio
	#relatorioComponent
	(iconClick)="iconClickFn($event)"
	*ngIf="alunoTemDados"
	[enableZebraStyle]="true"
	[showShare]="false"
	[table]="tableDataConfig"
	actionTitulo="Ações"
	class="pacto-relatorio-custom-padding"></pacto-relatorio>

<div *ngIf="!alunoTemDados" class="div-empty">
	<div class="div-interna-empty">
		<img class="icon-empty" src="assets/images/planilha.svg" />
		<div class="titulo-empty">
			O aluno ainda não possui nenhum atestado de aptidão física registrado!
		</div>
	</div>
</div>

<ng-template #columnAtestado>
	<span class="title-column" i18n="@@perfil-cliente-documentos:column-atestado">
		Atestado
	</span>
</ng-template>
<ng-template #columnContrato>
	<span class="title-column" i18n="@@perfil-cliente-documentos:column-contrato">
		Contrato
	</span>
</ng-template>
<ng-template #columnData>
	<span class="title-column" i18n="@@perfil-cliente-documentos:column-data">
		Data
	</span>
</ng-template>
<ng-template #columnVigencia>
	<span class="title-column" i18n="@@perfil-cliente-documentos:column-vigencia">
		Vigência
	</span>
</ng-template>

<ng-template #tipoAtestadoCell let-item="item">
	<div *ngIf="item?.tipoAtestado == 1">Atestado Aptidão Fisica</div>
	<div *ngIf="item?.tipoAtestado == 2">Atestado Contrato</div>
</ng-template>
