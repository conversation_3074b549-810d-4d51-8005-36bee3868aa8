import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import {
	AdmCoreApiComprovanteVacinaService,
	AdmCoreApiContratoService,
	AtestadoMedico,
	ComprovanteVacina,
} from "adm-core-api";
import {
	AdmLegadoAutorizarAcessoService,
	LinkAcessoRapidoService,
} from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import {
	AlunoAnexoAvaliacaoZW,
	AlunoBase,
	PerfilAcessoFuncionalidadeNome,
	TreinoApiAlunosService,
	TreinoApiAvaliacaoFisicaService,
} from "treino-api";
import {
	CatFileInputComponent,
	DialogService,
	PactoModalSize,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AtestadoMedicoTableComponent } from "./atestado-medico-table/atestado-medico-table.component";
import { TipoComprovanteVacinaEnum } from "./classes/tipo-comprovante-vacina.enum";
import { TipoUploadEnum } from "./classes/tipo-upload.enum";
import { UploadArquivo } from "./classes/upload-arquivo.model";
import { HistoricoAnexosTableComponent } from "./historico-anexos-table/historico-anexos-table.component";
import { ModalComprovanteVacinaComponent } from "./modal-comprovante-vacina/modal-comprovante-vacina.component";
import { ModalUploadArquivosComponent } from "./modal-upload-arquivos/modal-upload-arquivos.component";
import { PermissaoService } from "pacto-layout";
import { HistoricoParqTableComponent } from "./historico-parq-table/historico-parq-table.component";

declare var moment;

@Component({
	selector: "pacto-documenos-container",
	templateUrl: "./documentos-container.component.html",
	styleUrls: ["./documentos-container.component.scss"],
})
export class DocumentosContainerComponent implements OnInit {
	@ViewChild("historicoAnexosComponent", { static: false })
	private readonly historicoAnexosComponent: HistoricoAnexosTableComponent;

	@ViewChild("atestadoMedicoTableComponent", { static: false })
	private readonly atestadoMedicoTableComponent: AtestadoMedicoTableComponent;

	@ViewChild("documentosTableComponent", { static: false })
	private readonly documentosTableComponent: AtestadoMedicoTableComponent;

	@ViewChild("HistoricoParqTableComponent", { static: false })
	private readonly HistoricoParqTableComponent: HistoricoParqTableComponent;

	@ViewChild("traducao", { static: true })
	private readonly traducao: TraducoesXinglingComponent;

	@ViewChild("fileInputComponent", { static: true })
	private readonly fileInputComponent: CatFileInputComponent;

	private uploadArquivo: UploadArquivo;
	public readonly formGroup: FormGroup = new FormGroup({
		file: new FormControl(),
		nomeArquivo: new FormControl(),
	});

	// public historicoAnexos = new Array<any>();
	public atestadosMedicos: AtestadoMedico[];
	public documentosAlunos: Array<AlunoAnexoAvaliacaoZW>;

	// Contratos assinados
	public contratosAssinados = "00";

	// Comprovante de vacina
	public comprovantesVacinas = {
		primeiraDose: new ComprovanteVacina(),
		segundaDose: new ComprovanteVacina(),
		terceiraDose: new ComprovanteVacina(),
	};
	private matricula: number;
	public alunoBase: AlunoBase;
	private destroy$ = new Subject<void>();
	public isPermissaoExcluirAtestado: any;
	public isPermissaoExcluirDocumento: any;
	public permissaoExcluirAnexoTR: any;
	permissaoCadastrarDocumento: boolean = false;
	permissaoCadastrarAtestado: boolean = false;
	public chave: string;
	public empresa: string;
	public usuariozw: number;

	constructor(
		private readonly route: ActivatedRoute,
		private readonly admCoreApiContratos: AdmCoreApiContratoService,
		private readonly admCoreApiComprovanteVacinaService: AdmCoreApiComprovanteVacinaService,
		private readonly treinoApiAlunosService: TreinoApiAlunosService,
		private readonly notify: SnotifyService,
		private readonly modalService: DialogService,
		private readonly cd: ChangeDetectorRef,
		private readonly linkAcessoRapidoService: LinkAcessoRapidoService,
		private readonly sessionService: SessionService,
		private readonly autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private readonly permissaoService: PermissaoService,
		private readonly avaliacaoFisicaService: TreinoApiAvaliacaoFisicaService
	) {}

	ngOnInit() {
		this.chave = this.sessionService.chave;
		this.empresa = this.sessionService.empresaId;
		this.usuariozw = this.sessionService.codUsuarioZW;
		this.permissaoExcluirAnexoTR = this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.EXCLUIR_ANEXO_ALUNO
		);
		this.permissaoCadastrarDocumento =
			this.permissaoService.temPermissaoAdm("13.11");
		this.permissaoCadastrarAtestado =
			this.permissaoService.temPermissaoAdm("2.49");
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.formGroup.get("file").valueChanges.subscribe((file) => {
			if (file) {
				this.uploadArquivo = new UploadArquivo();
				const nomeArquivo = this.formGroup.get("nomeArquivo").value;
				this.uploadArquivo.tipo = TipoUploadEnum.NOVO;
				this.uploadArquivo.arquivoBase64 = file;
				this.uploadArquivo.nomeArquivo = nomeArquivo.split(".")[0];
				this.uploadArquivo.formatoArquivo = nomeArquivo.substring(
					nomeArquivo.lastIndexOf(".")
				);
				this.uploadArquivo.nomeArquivoApresentar = nomeArquivo;
				this.uploadArquivo.tituloModal = "Upload de arquivo";
				this.openModalUploadArquivos();
			}
		});
		this.treinoApiAlunosService
			.obterAlunoMatricula(this.matricula)
			.subscribe((aluno) => {
				this.alunoBase = aluno;
				this.alunoBase.matricula = this.matricula.toString();

				this.loadData();
			});
		this.formGroup.disable();
	}

	loadData() {
		this.admCoreApiContratos
			.quantidadeContratosAssinados(this.alunoBase.matricula)
			.subscribe((qtdContratos) => {
				this.contratosAssinados =
					qtdContratos < 10 ? "0" + qtdContratos : "" + qtdContratos;
				this.cd.detectChanges();
			});
		this.initComprovantesVacina();
		this.admCoreApiComprovanteVacinaService
			.findAllByMatricula(this.alunoBase.matricula)
			.subscribe((comprovantesVacinas) => {
				comprovantesVacinas.forEach((c) => {
					switch (c.tipo) {
						case TipoComprovanteVacinaEnum.PRIMEIRA_DOSE:
							this.comprovantesVacinas.primeiraDose = c;
							break;
						case TipoComprovanteVacinaEnum.SEGUNDA_DOSE:
							this.comprovantesVacinas.segundaDose = c;
							break;
						case TipoComprovanteVacinaEnum.TERCEIRA_DOSE:
							this.comprovantesVacinas.terceiraDose = c;
							break;
					}
				});
				this.cd.detectChanges();
			});
		this.isPermissaoExcluirAtestado = {
			chave: this.sessionService.chave,
			codigoUsuario: this.sessionService.codUsuarioZW,
			codigoEmpresa: this.sessionService.codigoEmpresa,
			funcionalidade: "ExclusaoAtestadoAptidaoFisica",
			permissao: "2.50 - Exclusão de atestado de aptidão física",
		};
		this.isPermissaoExcluirDocumento = {
			chave: this.sessionService.chave,
			codigoUsuario: this.sessionService.codUsuarioZW,
			codigoEmpresa: this.sessionService.codigoEmpresa,
			funcionalidade: "AlunoExcluirDocumento",
			permissao: "13.12 - Excluir documentos",
		};
	}

	openModalUploadArquivos() {
		if (this.uploadArquivo.tipo === TipoUploadEnum.NOVO) {
			this.uploadArquivo.sizeFileMb = this.getSizeFile(
				this.uploadArquivo.arquivoBase64
			);
			if (this.uploadArquivo.sizeFileMb > 8.0) {
				this.resetarFormularioUpload();
				this.notify.warning(
					this.traducao.getLabel("tamanho-limite-arquivo-ultrapassado")
				);
				return;
			}
		}
		if (!this.permissaoCadastrarDocumento && !this.permissaoCadastrarAtestado) {
			this.notify.error(
				"Você não possui as permissões  '13.11 - Cadastrar documentos' ou '2.49 - Lançar atestado de aptidão física'. " +
					"Fale com seu administrador e solicite a liberação."
			);
			return;
		}
		this.openModalUpload();
	}

	openModalUpload() {
		const modal = this.modalService.open(
			this.uploadArquivo.tituloModal,
			ModalUploadArquivosComponent,
			PactoModalSize.LARGE,
			"modal-upload-file"
		);
		modal.componentInstance.isPermitidolancamentoDeAtestadoDeAptidaoFisica =
			this.permissaoCadastrarAtestado;
		modal.componentInstance.isPermitidolancamentoDeDocumento =
			this.permissaoCadastrarDocumento;
		modal.componentInstance.uploadArquivo = this.uploadArquivo;
		modal.componentInstance.alunoBase = this.alunoBase;
		this.resetarFormularioUpload();

		modal.result
			.then((nameTableReload) => {
				if (nameTableReload === "documentos") {
					this.documentosTableComponent.loadData();
					this.HistoricoParqTableComponent.loadData();
				} else if (nameTableReload === "atestados") {
					this.atestadoMedicoTableComponent.loadData();
				}
			})
			.catch((error) => {
				console.log(error);
			});
	}

	getSizeFile(file): number {
		let sizeFile = 0.0;
		if (file) {
			const fileBaseB4 = file.split(",")[1];
			if (fileBaseB4.endsWith("==")) {
				sizeFile = fileBaseB4.length * (3 / 4) - 2;
			} else {
				sizeFile = fileBaseB4.length * (3 / 4) - 1;
			}
			sizeFile = sizeFile / 1024 / 1024;
		}
		return Number(sizeFile.toFixed(2));
	}

	resetarFormularioUpload() {
		this.fileInputComponent.filename = null;
		this.fileInputComponent.controlFc.reset();
		this.fileInputComponent.control.reset();
		this.fileInputComponent.nomeControl.reset();
	}

	isNullOrEmpty(value) {
		return value === null || value === undefined || value === "";
	}

	atestadosMedicosChangeEvent(atestadosMedicos: any) {
		this.atestadosMedicos = atestadosMedicos;
		// this.atualizarTableHistoricoAnexo();
	}

	documentosChangeEvent(documentos: any) {
		this.documentosAlunos = documentos;
		// this.atualizarTableHistoricoAnexo();
	}

	editEvent(uploadArquivo: UploadArquivo) {
		this.uploadArquivo = uploadArquivo;
		this.openModalUploadArquivos();
	}

	openModalComprovanteVacina(
		titulo: string,
		comprovanteVacina: ComprovanteVacina
	) {
		if (this.vacinado(comprovanteVacina)) {
			return;
		}

		this.cadastrarVacina();
	}

	private initComprovantesVacina() {
		this.comprovantesVacinas.primeiraDose = new ComprovanteVacina();
		this.comprovantesVacinas.primeiraDose.dataAplicacao = "---";
		this.comprovantesVacinas.primeiraDose.fabricante = "";
		this.comprovantesVacinas.primeiraDose.tipo =
			TipoComprovanteVacinaEnum.PRIMEIRA_DOSE;

		this.comprovantesVacinas.segundaDose = new ComprovanteVacina();
		this.comprovantesVacinas.segundaDose.dataAplicacao = "---";
		this.comprovantesVacinas.segundaDose.tipo =
			TipoComprovanteVacinaEnum.SEGUNDA_DOSE;
		this.comprovantesVacinas.segundaDose.fabricante = "";

		this.comprovantesVacinas.terceiraDose = new ComprovanteVacina();
		this.comprovantesVacinas.terceiraDose.dataAplicacao = "---";
		this.comprovantesVacinas.terceiraDose.tipo =
			TipoComprovanteVacinaEnum.TERCEIRA_DOSE;
		this.comprovantesVacinas.terceiraDose.fabricante = "";
		this.cd.detectChanges();
	}

	getDataFormatada(dataAplicacao: any): string {
		if (!this.isNullOrEmpty(dataAplicacao) && dataAplicacao !== "---") {
			return moment(dataAplicacao).format("DD/MM/YYYY");
		} else {
			return "---";
		}
	}

	vacinado(comprovante: ComprovanteVacina): boolean {
		if (
			!this.isNullOrEmpty(comprovante.dataAplicacao) &&
			comprovante.dataAplicacao !== "---"
		) {
			return true;
		} else {
			return false;
		}
	}

	getDescricaoFabricante(comprovante: ComprovanteVacina): string {
		if (comprovante.fabricante) {
			return comprovante.fabricante.length > 9
				? comprovante.fabricante.substring(0, 6) + "..."
				: comprovante.fabricante;
		}
	}

	cadastrarVacina(): void {
		try {
			this.linkAcessoRapidoService
				.obterLinkAppsPacto(
					this.sessionService.chave,
					this.sessionService.empresaId,
					this.sessionService.codigoUsuarioZw.toString(),
					"assinaturadigital",
					"vacina"
				)
				.pipe(takeUntil(this.destroy$))
				.subscribe((response: any) => {
					console.log({ response });
					let sufix = "";
					if (!response.content.linkAcesso.startsWith("http")) {
						sufix = this.sessionService.pathUrlZw;
					}

					const modalComprovanteVacina = this.modalService.open(
						"Cadastrar cartão de vacina",
						ModalComprovanteVacinaComponent,
						PactoModalSize.MEDIUM,
						"modal-comprovante-vacina"
					);

					modalComprovanteVacina.componentInstance.linkAcesso =
						sufix + response.content.linkAcesso;
					modalComprovanteVacina.componentInstance.qrCodeAscesso =
						sufix + response.content.qrCodeAcesso;
				});
		} catch (e) {
			console.log(e);
		}
	}
}
