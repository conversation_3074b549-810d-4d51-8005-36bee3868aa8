import { Component, Input, OnInit } from "@angular/core";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-corpo-frontal-simplificado",
	templateUrl: "./corpo-frontal-simplificado.component.html",
	styleUrls: ["./corpo-frontal-simplificado.component.scss"],
})
export class CorpoFrontalSimplificadoComponent implements OnInit {
	// GRUPO MUSCULAR SIMPLIFICADO FRONTAL

	@Input() listaAtiva: Array<any>;
	@Input() listaCompletaGrupoMuscular: Array<any>;
	@Input() podeSelecionarGrupoMuscular: boolean = false;

	tooltipAtivo: {
		nome: string;
		id: string;
		lado: "frontal" | "traseiro";
	};

	constructor(private snotifyService: SnotifyService) {}

	ngOnInit() {}

	pintarMusculo(nomeGrupo) {
		const grupoSelecionado = this.listaAtiva.some((obj) =>
			nomeGrupo.toUpperCase().includes(obj.nome.toUpperCase())
		);
		if (grupoSelecionado) {
			return "#366AE2";
		} else {
			return "#BCBEC2";
		}
	}

	tooltipParte(nomeGrupo) {
		this.tooltipAtivo = {
			nome: nomeGrupo.toUpperCase(),
			id: nomeGrupo.toUpperCase(),
			lado: "frontal",
		};
	}

	selecionarMusculo(nomeGrupo) {
		if (this.podeSelecionarGrupoMuscular) {
			const index = this.listaAtiva.findIndex((obj) =>
				nomeGrupo.toUpperCase().includes(obj.nome.toUpperCase())
			);
			if (index !== -1) {
				// Remover item existente da lista
				this.listaAtiva.splice(index, 1);

				// Segunda verificação para casos em que 2 grupos musculares estejam na mesma região
				const index2 = this.listaAtiva.findIndex((obj) =>
					nomeGrupo.toUpperCase().includes(obj.nome.toUpperCase())
				);
				if (index !== -1) {
					// Remover item existente da lista
					this.listaAtiva.splice(index, 1);
				}
			} else {
				// Utilizar sempre o primeiro, pois é a referência no desenho do corpo humano
				const listaNomeGrupo = nomeGrupo.split(";");
				if (listaNomeGrupo[0]) {
					const grupoEncontrado = this.listaCompletaGrupoMuscular.find(
						(grupo) =>
							listaNomeGrupo[0].toUpperCase().includes(grupo.nome.toUpperCase())
					);
					if (grupoEncontrado) {
						this.listaAtiva.push({
							id: grupoEncontrado.id,
							nome: grupoEncontrado.nome,
						});
					} else {
						this.snotifyService.warning(
							"O grupo muscular selecionado não foi encontrado na listagem de grupos musculares, " +
								"solicite ao suporte a revisão."
						);
					}
				}
			}
		}
	}
}
