<pacto-relatorio
	#tableItens
	*ngIf="table"
	[emptyStateMessage]="'Nenhum afastamento encontrado'"
	[showShare]="false"
	[table]="table"
	actionTitulo="Ações"
	idSuffix="pch-table-contrato-dependente-afastamento"></pacto-relatorio>

<ng-template #colunaTipoAfastamento let-item="item">
	<div [ngClass]="['situacao', item.tipoAfastamento.toLowerCase()]">
		<ng-container *ngIf="item.tipoAfastamento === 'CR'">Férias</ng-container>
		<ng-container *ngIf="item.tipoAfastamento === 'AT'">Atestado</ng-container>
	</div>
</ng-template>
