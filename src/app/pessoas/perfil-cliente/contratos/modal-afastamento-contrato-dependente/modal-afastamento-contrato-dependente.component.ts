import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	SelectFilterParamBuilder,
} from "ui-kit";
import { DatePipe } from "@angular/common";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import {
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-modal-afastamento-contrato-dependente",
	templateUrl: "./modal-afastamento-contrato-dependente.component.html",
	styleUrls: ["./modal-afastamento-contrato-dependente.component.scss"],
})
export class ModalAfastamentoContratoDependenteComponent implements OnInit {
	dadosPessoais;
	contratoDependente;
	@ViewChild("colunaDependenteTitular", { static: true })
	public colunaDependenteTitular;
	@ViewChild("colunaTipoAfastamento", { static: true })
	public colunaTipoAfastamento;
	@ViewChild("tableItens", { static: false })
	public tableItens: RelatorioComponent;
	public table: PactoDataGridConfig;
	@Output()
	update = new EventEmitter<any>();
	ferias = false;
	info;
	form: FormGroup;
	justificativaUrl;

	constructor(
		private modal: NgbActiveModal,
		private fb: FormBuilder,
		private snotifyService: SnotifyService,
		private readonly sessionService: SessionService,
		private readonly cd: ChangeDetectorRef,
		private readonly datePipe: DatePipe,
		private readonly restService: RestService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService
	) {}

	ngOnInit() {
		this.form = this.fb.group({
			dtInicio: this.fb.control("", [Validators.required]),
			dtFim: this.fb.control("", [Validators.required]),
			nrDias: this.fb.control(""),
			justificativa: this.fb.control("", [Validators.required]),
			observacao: this.fb.control(""),
		});
		this.form.get("dtInicio").setValue(new Date().getTime());
		this.justificativaUrl = this.restService.buildFullUrlCadAux(
			`justificativa-operacao`
		);
		this.obterInfo();
		this.form.get("dtFim").valueChanges.subscribe((value) => {
			this.alterouDtFinal();
		});
	}

	fecharHandler() {
		this.modal.close();
	}

	justificativaParamBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
				tipoOperacao: this.ferias ? "CR" : "AT",
			}),
		};
	};

	obterInfo() {
		this.admLegadoTelaClienteService
			.obterInfoAfastamentoContratoDependente(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				this.contratoDependente.codigo
			)
			.subscribe(
				(resp) => {
					this.info = resp.content;
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);
	}

	confirmar() {
		if (!this.form.get("dtInicio").value) {
			this.snotifyService.error("Informe a data inicio");
			return;
		}
		if (!this.form.get("dtFim").value) {
			this.snotifyService.error("Informe a data fim");
			return;
		}
		if (!this.form.get("justificativa").value) {
			this.snotifyService.error("Selecione a justificativa");
			return;
		}

		const body = {
			tipoOperacao: this.ferias ? "CR" : "AT",
			contratoDependente: this.contratoDependente.codigo,
			dtInicio: this.datePipe.transform(
				this.form.get("dtInicio").value,
				"yyyyMMdd"
			),
			dtFim: this.datePipe.transform(this.form.get("dtFim").value, "yyyyMMdd"),
			observacao: this.form.get("observacao").value,
			justificativa: this.form.get("justificativa").value.codigo,
		};
		this.admLegadoTelaClienteService
			.gravarAfastamentoContratoDependente(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				body
			)
			.subscribe(
				(resp) => {
					this.snotifyService.success("Afastamento lançado");
					this.update.emit("atualizar_contrato_dependente");
					this.fecharHandler();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					} else if (err.meta && err.meta.error) {
						this.snotifyService.error(err.meta.error);
					}
				}
			);
	}

	alterouDtInicio() {
		const dtInicio = this.form.get("dtInicio").value;
		const nrDias = this.form.get("nrDias").value;
		if (dtInicio && nrDias && nrDias > 0) {
			this.alterouNrDias();
		}
	}

	alterouNrDias() {
		const dtInicio = this.form.get("dtInicio").value;
		const nrDias = this.form.get("nrDias").value;
		if (dtInicio && nrDias && nrDias > 0) {
			const result = new Date(dtInicio);
			result.setDate(result.getDate() + (parseInt(nrDias) - 1));
			this.form.get("dtFim").setValue(result.getTime());
		}
	}

	alterouDtFinal() {
		const dtInicio = this.form.get("dtInicio").value;
		const dtFim = this.form.get("dtFim").value;
		if (dtInicio && dtFim) {
			const diffTime = Math.abs(dtFim - dtInicio);
			const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
			this.form.get("nrDias").setValue(diffDays + 1);
		}
	}
}
