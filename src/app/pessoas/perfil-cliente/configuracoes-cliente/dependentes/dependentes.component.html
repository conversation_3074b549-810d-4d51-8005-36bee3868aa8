<pacto-cat-card-plain>
	<pacto-cat-table-editable
		#tableData
		(confirm)="confirm($event)"
		(delete)="delete($event)"
		(edit)="onEdit($event)"
		(pageChangeEvent)="changePage($event)"
		(pageSizeChange)="changeSize($event)"
		[isEditable]="true"
		[showAddRow]="true"
		[table]="table"
		actionTitle="Ações"
		emptyStateMessage="Nenhum dependente cadastrado."
		i18n-actionTitle="@@label-acoes"
		idSuffix="table-dependentes"></pacto-cat-table-editable>
	<div class="d-flex justify-content-end">
		<pacto-cat-button
			(click)="saveConfig.emit()"
			[hidden]="!configCliFormValidation.isClientOnly"
			i18n-label="@@config-pessoa:btn-salvar"
			label="Salvar"
			size="LARGE"></pacto-cat-button>
	</div>
</pacto-cat-card-plain>
<ng-template #columnCliente>
	<span i18n="@@configuracoes-cliente-dependente:cliente">Cliente</span>
</ng-template>
<ng-template #columnGrauParentesco>
	<span i18n="@@configuracoes-cliente-dependente:grau-de-parentesco">
		Grau de parentesco
	</span>
</ng-template>
<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@dependentes:dependente-obrigatorio"
		xingling="dependente-obrigatorio">
		Você deve o cliente.
	</span>
	<span
		i18n="@@dependentes:parentesco-obrigatorio"
		xingling="parentesco-obrigatorio">
		Você deve informar o parentesco.
	</span>
	<span
		i18n="@@dependentes:dependente-duplicado"
		xingling="dependente-duplicado">
		Este aluno já foi cadastrado como dependente!
	</span>
</pacto-traducoes-xingling>
