import {
	ChangeDetector<PERSON>ef,
	Component,
	EventEmitter,
	Input,
	OnDestroy,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import {
	DialogService,
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { RestService } from "@base-core/rest/rest.service";
import { CrmApiGenericoService } from "crm-api";
import { DataService } from "../notify/data.service";
import { ActivatedRoute } from "@angular/router";
import { Subscription } from "rxjs";
import { SnotifyService } from "ng-snotify";
import { debounceTime } from "rxjs/operators";
import { ClienteDadosPessoais } from "adm-core-api";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import { Contato } from "../../../../../../projects/crm-api/src/lib/contato/contato.models";

@Component({
	selector: "pacto-crm-sms",
	templateUrl: "./crm-sms.component.html",
	styleUrls: ["./crm-sms.component.scss"],
})
export class CrmSmsComponent implements OnInit {
	@Input() dadosPessoais: ClienteDadosPessoais;
	@Output() reloadHist: EventEmitter<any> = new EventEmitter<any>();
	@Input() traducao: TraducoesXinglingComponent;
	form: FormGroup = new FormGroup({
		pesquisa: new FormControl(),
		telefoneCelular: new FormControl(),
		modeloMensagem: new FormControl(),
		observacao: new FormControl(""),
	});
	contato: Contato = {};

	constructor(
		private cd: ChangeDetectorRef,
		private dataService: DataService,
		private route: ActivatedRoute,
		private notify: SnotifyService,
		private rest: RestService,
		private modalService: DialogService,
		private crmService: CrmApiGenericoService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private readonly sessionService: SessionService
	) {}

	ngOnInit() {
		this.form.get("modeloMensagem").valueChanges.subscribe((v) => {
			if (!v) {
				return;
			}
			this.form.get("observacao").setValue(v.mensagem);
		});
		this.form.get("pesquisa").valueChanges.subscribe((value) => {
			if (!value) {
				return;
			}
			this.selecionouPesquisa();
		});
	}

	get _rest() {
		return this.rest;
	}

	selecionouPesquisa() {
		const pesquisa = this.form.get("pesquisa").value;
		if (pesquisa && pesquisa.codigo && pesquisa.codigo > 0) {
			this.crmService
				.geraLinkPesquisa(
					pesquisa.codigo,
					this.dadosPessoais.codigoCliente,
					this.sessionService.codigoUsuarioZw
				)
				.subscribe(
					(ret) => {
						const obs = this.form.get("observacao").value;
						this.form.get("observacao").setValue(obs + ret[0].url);
					},
					(httpErrorResponse) => {
						if (
							httpErrorResponse.error &&
							httpErrorResponse.error.meta &&
							httpErrorResponse.error.meta.message
						) {
							this.notify.error(httpErrorResponse.error.meta.message);
						} else {
							this.notify.error(
								this.traducao.getLabel("falha-ao-tentar-salvar-contato")
							);
						}
					}
				);
		}
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	// modeloMensagemSelectBuilder: SelectFilterParamBuilder = ( term ) =>
	// {
	//   return {
	//     page: '0', size: '50',
	//     filters: JSON.stringify( {
	//       situacoes: ['ATIVO'],
	//       quicksearchValue: term,
	//       quicksearchFields: ['nome']
	//     } )
	//   };
	// };
	// responseParser: SelectFilterResponseParser = ( response: ApiResponseList<any> ) =>
	// {
	//   return response.content;
	// };

	// removerAcentuacao( value: string ): string
	// {
	//   value = value.replace( new RegExp( '[âãàáä]', 'gi' ), 'a' );
	//   value = value.replace( new RegExp( '[êèéë]', 'gi' ), 'e' );
	//   value = value.replace( new RegExp( '[îíìï]', 'gi' ), 'i' );
	//   value = value.replace( new RegExp( '[ôõòóö]', 'gi' ), 'o' );
	//   value = value.replace( new RegExp( '[ûúùü]', 'gi' ), 'u' );
	//   value = value.replace( new RegExp( '[ýÿ]', 'gi' ), 'y' );
	//   value = value.replace( new RegExp( '[ç]', 'gi' ), 'c' );
	//   value = value.replace( new RegExp( 'ñ', 'gi' ), 'n' );
	//   value = value.replace( new RegExp( '[\'<>\\|]', 'gi' ), ' ' );
	//   value = value.replace( new RegExp( '/', 'gi' ), ' ' );
	//   return value;
	// }

	limpar() {
		this.form.get("pesquisa").setValue(null);
		this.form.get("telefoneCelular").setValue(null);
		this.form.get("modeloMensagem").setValue(null);
		this.form.get("observacao").setValue("");
	}

	getContato() {
		this.contato.fase = "";
		this.contato.contatoavulso = true;
		this.contato.dia = new Date().getTime().toString();
		this.contato.cliente = this.dadosPessoais.codigoCliente;
		this.contato.responsavelcadastro = this.sessionService.codigoUsuarioZw;
		this.contato.tipocontato = "CS";
		this.contato.observacao = this.form.get("observacao").value;
		this.contato.telefoneCelular = this.form.get("telefoneCelular").value;
		return this.contato;
	}

	validarDados() {
		if (this.form.get("telefoneCelular").value.length <= 0) {
			this.notify.error("Selecione um celular");
			return true;
		}
		if (!this.form.get("observacao").valid) {
			this.notify.error("Informe uma descrição");
			return true;
		}
		return false;
	}

	enviar() {
		if (this.validarDados()) {
			return;
		}
		this.admLegadoTelaClienteService
			.salvarHistoricoContato(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				this.dadosPessoais.codigoCliente,
				this.getContato()
			)
			.subscribe(
				(resp) => {
					this.reloadHist.emit();
					this.notify.success("SMS enviado");
					this.limpar();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.notify.error(err.meta.message);
					}
				}
			);
	}

	getTelefonesCelular() {
		const celulares = [];
		celulares.push({ id: "", label: "-" });
		for (let i = 0; this.dadosPessoais.telefones.length > i; i++) {
			if (this.dadosPessoais.telefones[i].tipo === "CE") {
				const numero = this.dadosPessoais.telefones[i].numero;
				celulares.push({ id: numero, label: numero });
			}
		}
		return celulares;
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		if (!response) {
			return [];
		}
		return response.content;
	};
}
