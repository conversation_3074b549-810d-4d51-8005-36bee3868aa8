<div class="row div-geral">
	<div *ngIf="isAulaExperimental()" class="col-6">
		<pacto-cat-form-select-filter
			[addEmptyOption]="true"
			[control]="formGroup.get('modalidade')"
			[endpointUrl]="_rest.buildFullUrlCrmCore('v1/generico/modalidade')"
			[paramBuilder]="selectBuilder"
			idKey="codigo"
			label="Modalidade"
			labelKey="nome"></pacto-cat-form-select-filter>
	</div>
	<div *ngIf="isAulaExperimental()" class="col-6">
		<pacto-cat-form-select-filter
			[addEmptyOption]="true"
			[control]="formGroup.get('professor')"
			[options]="professoresOptions"
			idKey="id"
			label="Professor"
			labelKey="label"></pacto-cat-form-select-filter>
	</div>
	<div *ngIf="isVisita() || isLigacao()" class="col-12">
		<pacto-cat-form-select-filter
			[addEmptyOption]="true"
			[control]="formGroup.get('colaborador')"
			[options]="consultoresOptions"
			idKey="id"
			label="Colaborador"
			labelKey="label"></pacto-cat-form-select-filter>
	</div>
	<div class="col-6">
		<pacto-cat-form-datepicker
			[control]="formGroup.get('data')"
			[label]="'Data'"></pacto-cat-form-datepicker>
	</div>
	<div class="col-6">
		<pacto-cat-form-input
			[control]="formGroup.get('horario')"
			[textMask]="{ guide: true, mask: timeMask }"
			label="Horário"></pacto-cat-form-input>
	</div>
	<div class="col-12 actions">
		<pacto-cat-button
			(click)="close()"
			label="Cancelar"
			size="LARGE"
			type="OUTLINE"></pacto-cat-button>
		<pacto-cat-button
			(click)="salvar()"
			label="Salvar Agendamento"
			size="LARGE"
			type="PRIMARY"></pacto-cat-button>
	</div>
</div>
