import {
	Component,
	OnInit,
	Input,
	OnChanges,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	ViewChild,
	EventEmitter,
	Output,
} from "@angular/core";
import { Router } from "@angular/router";

import { Observable, zip } from "rxjs";
import { map } from "rxjs/operators";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import { ModalService } from "@base-core/modal/modal.service";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import {
	AlunoBase,
	AvaliacaoFisica,
	PerfilAcessoRecursoNome,
	TreinoApiAlunosService,
	TreinoApiAvaliacaoCatalogoService,
	TreinoApiAvaliacaoFisicaService,
} from "treino-api";
import {
	PactoColor,
	PactoModalSize,
	PieChartSet,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ModalReferenciaImcComponent } from "../modal-referencia-imc/modal-referencia-imc.component";
import { ModalReferenciaPercentualGorduraComponent } from "../modal-referencia-percentual-gordura/modal-referencia-percentual-gordura.component";

declare var moment;

@Component({
	selector: "pacto-ultima-avaliacao",
	templateUrl: "./ultima-avaliacao.component.html",
	styleUrls: ["./ultima-avaliacao.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UltimaAvaliacaoComponent implements OnInit {
	@Input() matricula: string;
	aluno: AlunoBase;
	@Input() mensagem: string;
	@ViewChild("composicaoLabels", { static: true })
	composicaoLabels: TraducoesXinglingComponent;
	@ViewChild("xinglingRemoverAvaliacao", { static: true })
	xinglingRemoverAvaliacao: TraducoesXinglingComponent;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@Output() update: EventEmitter<any> = new EventEmitter();

	ultima: AvaliacaoFisica;
	todas: AvaliacaoFisica[];
	composicaoSeries: PieChartSet[] = [];
	/** Nova propriedade para armazenar as legendas, na mesma ordem de `composicaoSeries` */
	legendLabels: string[] = [];

	avaliacaoParaRemover: AvaliacaoFisica;
	percentualGordura;
	produtoAvaliacao: any;
	isProdutoValido: any;
	integracaoZW = false;
	colors: PactoColor[] = [
		PactoColor.HELLBOY_PRI,
		PactoColor.PEQUIZAO_PRI,
		PactoColor.CINZA_06,
		PactoColor.AZULIM_PRI,
		PactoColor.CINZA_PRI,
	];
	sexoAluno: string;

	constructor(
		private catalogoService: TreinoApiAvaliacaoCatalogoService,
		private avaliacaoService: TreinoApiAvaliacaoFisicaService,
		private treinoConfigService: TreinoConfigCacheService,
		private snotify: SnotifyService,
		private pactoModal: ModalService,
		private router: Router,
		private modal: NgbModal,
		private cd: ChangeDetectorRef,
		private alunoService: TreinoApiAlunosService,
		private restService: RestService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.initDados();
	}

	initDados() {
		this.alunoService
			.obterAlunoCompletoPorMatricula(this.matricula)
			.subscribe((alunoResponse) => {
				this.aluno = alunoResponse;
				this.sexoAluno = this.aluno.sexo;
				this.setUp().subscribe((resposta) => {
					this.prepareComposicaoChart();
					this.cd.detectChanges();
				});
			});
	}

	get validarProduto() {
		return this.treinoConfigService.configuracoesAvaliacao
			.validar_produto_avaliacao;
	}

	get informacoesComposicaoDisponiveis() {
		if (!this.ultima) {
			return false;
		} else {
			const bi = this.ultima.alunoBI;
			const gordura = !isNaN(bi.composicaoPorcentualGordura);
			const ossos = !isNaN(bi.composicaoPorcentualOssos);
			const residuos = !isNaN(bi.composicaoPorcentualResiduos);
			const musculo = !isNaN(bi.composicaoPorcentualMusculos);
			const peso = !isNaN(bi.peso);
			return peso && gordura && ossos && residuos && musculo;
		}
	}

	private prepareComposicaoChart() {
		this.composicaoSeries = [];
		this.legendLabels = [];
		if (!this.ultima) {
			return;
		}
		const bi = this.ultima.alunoBI;
		const keys = this.graficoBioImpedanciaManual(this.ultima)
			? ["composicaoMassaMagra", "composicaoMassaGorda"]
			: ["gordura", "residuos", "musculos", "ossos"];
		if (bi.naoInformado > 0.0) {
			keys.push("naoInformado");
		}
		this.percentualGordura = bi.composicaoPorcentualGordura
			? Math.trunc(bi.composicaoPorcentualGordura)
			: 0.0;
		keys.forEach((key) => {
			const label = this.composicaoLabels.getLabel(key);
			const value = bi[key];
			this.composicaoSeries.push({ name: label, data: value });
			this.legendLabels.push(label);
		});
	}

	private graficoBioImpedanciaManual(ultima: AvaliacaoFisica): boolean {
		return !!ultima.alunoBI && ultima.alunoBI.composicaoMassaGorda > 0;
	}

	get idade() {
		let idade = 0;
		if (this.aluno && this.aluno.dataNascimento) {
			const dataNascimento = moment(this.aluno.dataNascimento);
			const hoje = moment();
			const duration = moment.duration(hoje.diff(dataNascimento));
			idade = Math.trunc(duration.asMonths());
		}
		return idade;
	}

	validarPermissaoProduto() {
		if (this.aluno.idade === 0) {
			const idade = Math.trunc(this.idade / 12);
			this.aluno.idade = idade;
		}
		if (this.validarProduto && this.integracaoZW) {
			this.produtoAvaliacao = JSON.parse(
				this.treinoConfigService.configuracoesAvaliacao.produto_avaliacao
			);
			this.catalogoService
				.isProdutoVigente(this.aluno.id, this.produtoAvaliacao)
				.subscribe((result) => {
					this.isProdutoValido = result;
					const stringValue = result as string;
					this.isProdutoValido = /true/i.test(stringValue);
					if (this.isProdutoValido) {
						if (
							this.aluno.sexo === undefined ||
							this.aluno.idade === undefined
						) {
							this.snotify.warning(
								this.traducoes.getLabel("CAMPOS_OBRIGATORIOS")
							);
						} else {
							this.router.navigate([
								"/avaliacao",
								"avaliacoes-aluno",
								this.aluno.id,
								"avaliacao",
								"novo",
							]);
						}
					} else {
						if (stringValue.includes("usado.")) {
							this.snotify.error(this.traducoes.getLabel("PRODUTO_ULTILIZADO"));
							return false;
						} else {
							this.snotify.error(this.traducoes.getLabel("VALIDAR_PRODUTO"));
							return false;
						}
					}
				});
		} else if (this.aluno.sexo && this.aluno.idade && !this.integracaoZW) {
			this.router.navigate([
				"/avaliacao",
				"avaliacoes-aluno",
				this.aluno.id,
				"avaliacao",
				"novo",
			]);
		} else {
			if (
				!this.aluno.sexo ||
				this.aluno.idade === undefined ||
				this.aluno.idade === null
			) {
				this.snotify.warning(this.traducoes.getLabel("CAMPOS_OBRIGATORIOS"));
			} else {
				this.router.navigate([
					"/avaliacao",
					"avaliacoes-aluno",
					this.aluno.id,
					"avaliacao",
					"novo",
				]);
			}
		}
	}

	criarPrimeiraAvaliacao(matricula, aluno) {
		this.matricula = matricula;
		this.aluno = aluno;
		this.validarCampos();
	}

	validarCampos() {
		if (this.aluno.idade > 125) {
			this.snotify.error("Data de nascimento do cadastro inválida.");
			return false;
		}
		const permissao = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.LANCAR_AVALICAO_RETROATIVA
		);
		if (permissao && permissao.incluir) {
			this.validarPermissaoProduto();
		} else {
			this.snotify.warning(
				"Seu usuário não possui permissão, procure seu administrador"
			);
		}
	}

	criarHandler() {
		this.validarCampos();
	}

	get objetivosLabel() {
		let result = "";
		if (this.ultima.objetivos) {
			this.ultima.objetivos.forEach((objetivo, index) => {
				if (index > 0) {
					result += ", ";
				}
				result += objetivo.toLowerCase();
			});
		}
		return result ? result : "-";
	}

	composicaoLabelFn() {
		return (value) => `${value}%`;
	}

	composicaoLabelBorderFn() {
		return (value) => {
			const arredondado = Math.round(value * 100) / 100;
			return `${arredondado} kg`;
		};
	}

	get altura() {
		const ultima = this.ultima;
		return (ultima && ultima.alunoBI && ultima.alunoBI.altura) || "-";
	}

	get peso() {
		const ultima = this.ultima;
		return (ultima && ultima.alunoBI && ultima.alunoBI.peso) || "-";
	}

	get paFc() {
		const ultima = this.ultima;
		const pressaoArterial =
			(ultima && ultima.pesoAltura && ultima.pesoAltura.pressaoArterial) || "";
		const frequencia =
			(ultima && ultima.pesoAltura && ultima.pesoAltura.frequencia) || "";
		let separator = pressaoArterial ? " - " : "";
		return `${pressaoArterial}${separator}${frequencia}`;
	}

	get avaliadorNome() {
		return this.ultima && this.ultima.avaliador && this.ultima.avaliador.nome;
	}

	removeAvaliacaoHandler(avaliacao: AvaliacaoFisica) {
		this.avaliacaoParaRemover = avaliacao;
		this.cd.detectChanges();
		const xingling = this.xinglingRemoverAvaliacao;
		const modal = this.pactoModal.confirm(
			xingling.getLabel("titulo"),
			xingling.getLabel("body")
		);
		const origem = "Manual";
		modal.result.then(
			() => {
				this.avaliacaoService
					.removerAvaliacaoFisicaOrigem(avaliacao.id, origem)
					.subscribe(() => {
						this.setUp();
						this.snotify.success(xingling.getLabel("sucesso"));
						this.update.emit(true);
						this.initDados();
						this.cd.detectChanges();
					});
			},
			() => {}
		);
	}

	composicaoReferenciaHandler() {
		const sexoPassado = this.aluno.sexo || "M";
		const modalRef = this.pactoModal.open(
			"Referência",
			ModalReferenciaPercentualGorduraComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.sexoAluno = sexoPassado;
	}

	imcReferenciaHandler() {
		this.pactoModal.open(
			"Referência IMC",
			ModalReferenciaImcComponent,
			PactoModalSize.LARGE
		);
	}

	private setUp(): Observable<any> {
		const $ultimaAvaliacao = this.catalogoService.obterUltimaAvaliacaoAluno(
			this.aluno.id
		);
		const $todasAvaliacoes = this.catalogoService.obterTodasAvaliacoesAluno(
			this.aluno.id
		);
		return zip($ultimaAvaliacao, $todasAvaliacoes).pipe(
			map((result) => {
				this.ultima = result[0];
				this.todas = result[1];
				return true;
			})
		);
	}

	get urlLog() {
		return this.restService.buildFullUrl(
			`log/avaliacoes-aluno/${this.aluno.id}`
		);
	}
}
