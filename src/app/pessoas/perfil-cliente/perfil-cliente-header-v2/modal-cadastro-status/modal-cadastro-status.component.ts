import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";

@Component({
	selector: "pacto-modal-cadastro-status",
	templateUrl: "./modal-cadastro-status.component.html",
	styleUrls: ["./modal-cadastro-status.component.scss"],
})
export class ModalCadastroStatusComponent implements OnInit {
	@Input() labelCadastro: string;
	@Input() bodyInfo: string;
	@Input() bodyText: string;
	@Input() labelCancel: string;
	@Input() showBtn: boolean = true;
	@Output() result: EventEmitter<any> = new EventEmitter();

	constructor() {}

	ngOnInit() {}

	exit() {
		this.result.emit({ status: false });
	}

	confirm() {
		this.result.emit({ status: true });
	}
}
