import { OnInit, ChangeDetectorRef, Component, ViewChild } from "@angular/core";
import {
	DialogService,
	PactoModalSize,
	PactoDataGridConfig,
	RelatorioComponent,
} from "ui-kit";
import { ModalConvidadosComponent } from "../../modal-convidados/modal-convidados.component";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { AdmLegadoTelaClienteService } from "adm-legado-api";

declare var moment;

@Component({
	selector: "pacto-historico-convidados",
	templateUrl: "./historico-convidados.component.html",
	styleUrls: ["./historico-convidados.component.scss"],
})
export class HistoricoConvidadosComponent implements OnInit {
	matricula;
	table: PactoDataGridConfig;
	cliente;
	chave;
	usuarioLogado;
	data = {
		content: [],
	};

	@ViewChild("tableData", { static: true }) tableData: RelatorioComponent;

	constructor(
		private dialogService: DialogService,
		private modal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private admLegadoService: AdmLegadoTelaClienteService,
		private snotify: SnotifyService
	) {}

	ngOnInit() {
		this.table = new PactoDataGridConfig({
			rowClick: false,
			pagination: false,
			dataAdapterFn: () => this.data,
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: true,
				},
				{
					nome: "convidado",
					titulo: "Convidado",
					visible: true,
				},
				{
					nome: "dia",
					titulo: "Data do convite",
					visible: true,
					valueTransform: (v) => {
						return v ? moment(v).format("DD/MM/YYYY") : v;
					},
				},
				{
					nome: "situacao",
					titulo: "Situação",
					visible: true,
				},
			],
		});

		this.admLegadoService
			.historicoConvidados(this.chave, this.cliente)
			.subscribe((resp) => {
				this.data = resp;
				this.tableData.reloadData();
			});
	}

	voltar() {
		this.modal.close();
		const modalRef = this.dialogService.open(
			"Adicionar convidado",
			ModalConvidadosComponent,
			PactoModalSize.MEDIUM,
			"convidado-modal"
		);
		modalRef.componentInstance.matricula = this.matricula;
		modalRef.componentInstance.cliente = this.cliente;
		modalRef.componentInstance.chave = this.chave;
		modalRef.componentInstance.usuarioLogado = this.usuarioLogado;
	}
}
