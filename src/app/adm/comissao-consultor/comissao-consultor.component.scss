@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var";

.form-container {
	padding: 20px;
	max-width: 1000px;
	margin: 0 auto;
}

.form-row {
	display: flex;
	align-items: flex-start;
	margin-bottom: 16px;
	min-height: 48px;

	.label-column {
		width: 250px;
		min-width: 250px;
		padding-right: 16px;
		padding-top: 8px;

		ds3-field-label {
			font-weight: 600;
			color: #383b3e;
			font-size: 14px;
		}
	}

	.input-column {
		flex: 1;
		max-width: 600px;

		ds3-select,
		input,
		ds3-input-date {
			width: 100%;
		}
	}
}

.radio-group {
	display: flex;
	//flex-direction: column;
	gap: 8px;

	.radio-item {
		display: flex;
		align-items: center;
		gap: 8px;

		input[type="radio"] {
			margin: 0;
		}

		label {
			font-size: 14px;
			color: #383b3e;
			cursor: pointer;
		}
	}
}

.checkbox-group {
	display: flex;
	//flex-direction: column;
	gap: 8px;

	.checkbox-item {
		display: flex;
		align-items: center;
		gap: 8px;

		input[type="checkbox"] {
			margin: 0;
		}

		label {
			font-size: 14px;
			color: #383b3e;
			cursor: pointer;
		}
	}
}

.checkbox-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
	gap: 12px;

	.checkbox-item {
		display: flex;
		align-items: center;
		gap: 4px;

		input[type="checkbox"] {
			margin: 0;
		}

		label {
			font-size: 14px;
			color: #383b3e;
			cursor: pointer;
		}
	}
}

.periodo-group {
	display: flex;
	align-items: center;
	gap: 12px;

	.periodo-separator {
		font-size: 14px;
		color: #383b3e;
		font-weight: 500;
	}

	ds3-input-date {
		flex: 1;
	}
}

.consulta-group {
	display: flex;
	align-items: center;
	gap: 12px;

	input {
		flex: 1;
	}

	button {
		min-width: 100px;
	}
}

.observacao-text {
	font-size: 14px;
	/* color: #28a745; */
	//font-style: italic;
	padding: 8px;
	background-color: #f8f9fa;
	/* border-left: 4px solid #28a745; */
	border-radius: 8px;
	display: flex;
	align-items: center;
	gap: 8px;
}

.form-actions {
	width: 100%;
	display: grid;
	grid-template-columns: 2.5fr 1fr;
	gap: 16px;
	margin: 20px;
	align-items: end;

	button {
		min-width: 160px;
	}
}

// Estilos dos modais
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
}

.modal-content {
	background: white;
	border-radius: 8px;
	width: 90%;
	max-width: 600px;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 20px;
	border-bottom: 1px solid #e9ecef;
	background-color: #f8f9fa;

	h3 {
		margin: 0;
		font-size: 18px;
		font-weight: 600;
		color: #383b3e;
	}

	.modal-close {
		background: none;
		border: none;
		font-size: 24px;
		cursor: pointer;
		color: #6c757d;
		padding: 0;
		width: 30px;
		height: 30px;
		display: flex;
		align-items: center;
		justify-content: center;

		&:hover {
			color: #383b3e;
		}
	}
}

.modal-body {
	padding: 20px;
	max-height: 60vh;
	overflow-y: auto;
}

.busca-form {
	margin-bottom: 20px;

	.busca-row {
		display: flex;
		gap: 12px;
		align-items: center;

		ds3-select {
			width: 120px;
		}

		input {
			flex: 1;
		}

		button {
			min-width: 100px;
		}
	}
}

.resultados-tabela {
	table {
		width: 100%;
		border-collapse: collapse;
		border: 1px solid #d7d8db;

		th,
		td {
			padding: 12px;
			text-align: left;
			border-bottom: 1px solid #e9ecef;
		}

		th {
			background-color: #f8f9fa;
			font-weight: 600;
			color: #383b3e;
		}

		td {
			color: #495057;
		}

		.link-button {
			background: none;
			border: none;
			color: #007bff;
			text-decoration: underline;
			cursor: pointer;
			font-size: 14px;

			&:hover {
				color: #0056b3;
			}
		}
	}
}

// Responsividade
@media (max-width: 768px) {
	.form-container {
		padding: 10px;
	}

	.form-row {
		flex-direction: column;
		align-items: stretch;

		.label-column {
			width: 100%;
			min-width: 100%;
			padding-right: 0;
			padding-bottom: 8px;
		}

		.input-column {
			max-width: 100%;
		}
	}

	.periodo-group {
		flex-direction: column;
		align-items: stretch;
		gap: 8px;

		.periodo-separator {
			text-align: center;
		}
	}

	.consulta-group {
		flex-direction: column;
		align-items: stretch;
		gap: 8px;

		button {
			width: 100%;
		}
	}

	.checkbox-grid {
		grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
	}

	.form-actions {
		flex-direction: column;
		align-items: stretch;

		button {
			width: 100%;
		}
	}

	.modal-content {
		width: 95%;
		margin: 10px;
	}

	.busca-row {
		flex-direction: column;
		align-items: stretch;

		ds3-select,
		input,
		button {
			width: 100%;
		}
	}
}

.itens-group-operador {
	display: grid;
	grid-template-columns: 1fr 2fr;
	gap: 8px;
}

.div-operador-resp {
	display: grid;
	grid-template-columns: 1fr 2fr;
	gap: 8px;
}

.margin {
	margin-top: 32px;
}

.div-desconsiderar-cheques-pendencia {
	display: flex;
	gap: 8px;
}

.item-click {
	cursor: pointer;
}

.div-observacoes {
	display: grid;
	gap: 10px;
	grid-template-columns: 1fr;
}

.div-btns-acao {
	display: grid;
	grid-template-columns: 1fr 1fr;
	height: 45px;
	min-width: 107px;
	gap: 16px;
}

.text-info {
	font-family: Poppins;
	font-size: 12px;
	font-weight: 400;
	color: hsla(223, 5%, 30%, 1) !important;
	margin-top: 5px;
}