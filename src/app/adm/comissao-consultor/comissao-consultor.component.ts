import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import {
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { AdmCoreApiEmpresaService } from "adm-core-api";
import {
	ZwBootComissaoConsultorService,
	Consultor,
	Atendente,
	Duracao,
} from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import {
	LoaderService,
} from "ui-kit";

@Component({
	selector: "app-comissao-consultor",
	templateUrl: "./comissao-consultor.component.html",
	styleUrls: ["./comissao-consultor.component.scss"],
})
export class ComissaoConsultorComponent implements OnInit {
	formGroup: FormGroup;
	empresas: any[] = [];
	consultores: any[] = [];
	colaboradores: any[] = [];
	duracoes: any[] = [];
	configuracao: any = {};

	// Opções dinâmicas
	tiposDados = [
		{ value: 1, label: "Por Receita/Despesa" },
		{ value: 2, label: "Por Competência" },
		{ value: 3, label: "Por Faturamento Recebido" },
		{ value: 4, label: "Por Faturamento" },
	];

	operadoresResponsaveis = [
		{ value: 1, label: "Contrato/Produto" },
		{ value: 2, label: "Recebimento" },
	];

	tiposPagamento = [
		{ value: "MA", label: "Matrícula" },
		{ value: "RE", label: "Rematrícula" },
		{ value: "RN", label: "Renovação" },
	];

	impressoesPor = [
		{ value: "CO", label: "Consultor Contrato" },
		{ value: "CA", label: "Consultor Atual" },
		{ value: "SO", label: "Somente Operador" },
		{ value: "RL", label: "Somente Responsável Lançamento" },
	];

	modosVisualizacao = [
		{ value: "AP", label: "Detalhado" },
		{ value: "A", label: "Totalizado por Aluno" },
		{ value: "S", label: "Totalizado por Duração" },
	];

	valoresComissoes = [
		{ value: "PORC", label: "Por porcentagem" },
		{ value: "FIXO", label: "Por valor fixo" },
	];

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private fb: FormBuilder,
		private loaderService: LoaderService,
		private snotifyService: SnotifyService,
		private sessionService: SessionService,
		private empresaService: AdmCoreApiEmpresaService,
		private zwBootComissaoConsultorService: ZwBootComissaoConsultorService
	) {}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	ngOnInit() {
		this.initFormGroup();
		this.carregarEmpresas();
		this.carregarPeriodicidades();
		this.carregarColaboradores();
		this.carregarConsultores();
		this.carregarConfiguracoes();
		this.cd.detectChanges();
	}

	private initFormGroup() {
		this.formGroup = this.fb.group({
			empresa: new FormControl(this.sessionService.empresaId),
			atendente: new FormControl(null),
			consultor: new FormControl(null),
			tipoRelatorioEscolhido: new FormControl(1, Validators.required),
			dataInicio: new FormControl(new Date()),
			dataFinal: new FormControl(new Date()),
			dataInicioR: new FormControl(null),
			dataContratosLancadosAPartir: new FormControl(null),
			dataCompetencia: new FormControl(null),
			tipoContrato: new FormControl(null),
			opcaoImpressao: new FormControl("CO"),
			visualizacao: new FormControl("AP"),
			tipoValorComissoes: new FormControl("PORC"),
			tipoResponsavel: new FormControl(1),
			retirarRecebiveisComPendecia: new FormControl(false),
			considerarCompensacaoOriginal: new FormControl(false),
			duracoes: new FormControl([]),
		});

		// Observar mudanças no tipo de dados
		this.formGroup.get("tipoRelatorioEscolhido").valueChanges.subscribe((tipo) => {
			this.onTipoDadosChange(tipo);
		});
	}

	private carregarEmpresas() {
		this.empresaService.findAll().subscribe(
			(response) => {
				this.empresas = response.content || [];
				this.cd.detectChanges();
			},
			(error) => {
				console.error("Erro ao carregar empresas:", error);
			}
		);
	}

	private carregarColaboradores() {
		this.zwBootComissaoConsultorService.colaboradores().subscribe(
			(response) => {
				this.colaboradores = response.content || [];
				this.cd.detectChanges();
			},
			(error) => {
				console.error("Erro ao carregar colaboradores:", error);
			}
		);
	}

	private carregarConfiguracoes() {
		this.zwBootComissaoConsultorService.configuracoes().subscribe(
			(response) => {
				this.configuracao = response.content || {};
				this.cd.detectChanges();
			},
			(error) => {
				console.error("Erro ao carregar configuracoes:", error);
			}
		);
	}

	private carregarConsultores() {
		this.zwBootComissaoConsultorService.consultores().subscribe(
			(response) => {
				this.consultores = response.content || [];
				this.cd.detectChanges();
			},
			(error) => {
				console.error("Erro ao carregar consultores:", error);
			}
		);
	}

	private carregarPeriodicidades() {
		this.zwBootComissaoConsultorService.periodicidades().subscribe(
			(response) => {
				this.duracoes = response.content || [];
				this.cd.detectChanges();
			},
			(error) => {
				console.error("Erro ao carregar durações:", error);
			}
		);
	}

	private onTipoDadosChange(tipo) {
		// Reset campos condicionais
		this.formGroup.get("dataInicio").setValue(null);
		this.formGroup.get("dataFim").setValue(null);
		this.formGroup.get("competencia").setValue(null);
		this.formGroup.get("somenteRecebimentoAPartir").setValue(null);
		this.formGroup.get("somenteContratosLancadosAPartir").setValue(null);

		// Ajustar validações baseado no tipo
		if (tipo === 2) {
			this.formGroup.get("competencia").setValidators([Validators.required]);
			this.formGroup.get("dataInicio").clearValidators();
			this.formGroup.get("dataFim").clearValidators();
		} else {
			this.formGroup.get("dataInicio").setValidators([Validators.required]);
			this.formGroup.get("dataFim").setValidators([Validators.required]);
			this.formGroup.get("competencia").clearValidators();
		}

		this.formGroup.get("dataInicio").updateValueAndValidity();
		this.formGroup.get("dataFim").updateValueAndValidity();
		this.formGroup.get("competencia").updateValueAndValidity();
	}

	gerarRelatorio(tipo) {
		if (this.formGroup.invalid) {
			this.snotifyService.error("Preencha todos os campos obrigatórios.");
			return;
		}

		const filtro = this.formGroup.getRawValue();
		console.log(filtro);
		this.loaderService.initForce();
		this.zwBootComissaoConsultorService.gerarRelatorio(tipo, filtro).subscribe(
			(response) => {
				this.loaderService.stopForce();
				if (response.content === "sem_registros") {
					this.snotifyService.warning(
						"Não foi encontrado nenhum recibo no período informado."
					);
				} else {
					window.open(response.content, "_blank");
				}
			},
			(error) => {
				this.loaderService.stopForce();
				this.snotifyService.error(
					error.error.meta.message || "Erro ao gerar relatório"
				);
			}
		);
	}

	// Getters para campos condicionais
	get isAdministrador(): boolean {
		return false;
	}

	get showPeriodo(): boolean {
		const tipo = this.formGroup.get("tipoRelatorioEscolhido");
		return tipo && tipo.value && tipo.value !== 2;
	}

	get showCompetencia(): boolean {
		const tipo = this.formGroup.get("tipoRelatorioEscolhido");
		return tipo && tipo.value === 2;
	}

	get showRecebimentoAPartir(): boolean {
		const tipo = this.formGroup.get("tipoRelatorioEscolhido");
		return (
			tipo &&
			(tipo.value === 1 ||
				tipo.value === 3)
		);
	}

	get showContratosLancadosAPartir(): boolean {
		const tipo = this.formGroup.get("tipoRelatorioEscolhido");
		return tipo && tipo.value !== 4;
	}

	get showOperadorResponsavel() {
		const tipo = this.formGroup.get("tipoRelatorioEscolhido");
		return tipo && (tipo.value === 3 || tipo.value === 1);
	}

	get showResponsavelLancamento() {
		const tipo = this.formGroup.get("tipoRelatorioEscolhido");
		return tipo && tipo.value === 4;
	}

	// tiposDados = [
	// 	{ value: 1, label: "Por Receita/Despesa" },
	// 	{ value: 2, label: "Por Competência" },
	// 	{ value: 3, label: "Por Faturamento Recebido" },
	// 	{ value: 4, label: "Por Faturamento" },
	// ];

	get showResponsavelContrato() {
		const tipo = this.formGroup.get("tipoResponsavel");
		return tipo && tipo.value === 1;
	}

	get showConsiderarDataCompensacao(): boolean {
		const tipo = this.formGroup.get("tipoRelatorioEscolhido");
		return tipo && tipo.value === 1;
	}

	get showChequesEmPendencia(): boolean {
		const tipo = this.formGroup.get("tipoRelatorioEscolhido");
		return (tipo && (tipo.value === 1 || tipo.value === 3));
	}

	get nomeEmpresa(): string {
		return this.sessionService.currentEmpresa.nome;
	}

	onDuracaoChange(duracao: number, checked: boolean) {
		const duracoes = this.formGroup.get("duracoes");
		const currentValue = duracoes ? duracoes.value || [] : [];

		if (checked) {
			if (!currentValue.includes(duracao)) {
				currentValue.push(duracao);
			}
		} else {
			const index = currentValue.indexOf(duracao);
			if (index > -1) {
				currentValue.splice(index, 1);
			}
		}

		if (duracoes) {
			duracoes.setValue(currentValue);
		}
	}

	onTipoPagamentoChange(tipo: string, checked: boolean) {
		const tiposPagamento = this.formGroup.get("tiposPagamento");
		const currentValue = tiposPagamento ? tiposPagamento.value || [] : [];

		if (checked) {
			if (!currentValue.includes(tipo)) {
				currentValue.push(tipo);
			}
		} else {
			const index = currentValue.indexOf(tipo);
			if (index > -1) {
				currentValue.splice(index, 1);
			}
		}

		if (tiposPagamento) {
			tiposPagamento.setValue(currentValue);
		}
	}

	isDuracaoSelecionada(duracao: number): boolean {
		const duracoes = this.formGroup.get("duracoes");
		const currentValue = duracoes ? duracoes.value || [] : [];
		return currentValue.includes(duracao);
	}

	isTipoPagamentoSelecionado(tipo: string): boolean {
		const tiposPagamento = this.formGroup.get("tipoContrato");
		const currentValue = tiposPagamento ? tiposPagamento.value || [] : [];
		return currentValue.includes(tipo);
	}

	get msgImpressao() {
		const tipo = this.formGroup.get("opcaoImpressao");
		if (tipo) {
			if (tipo.value === "CO") {
				return "Irá trazer a comissão agrupada pelos consultores do contrato (no caso de produtos, consultor do cliente na época da venda)";
			} else if (tipo.value === "CA") {
				return "Trará o relatório de comissão agrupado pelo consultor atual do cliente";
			} else if (tipo.value === "SO") {
				return "Agrupará o relatório de comissão por quem lançou o recibo no sistema";
			} else if (tipo.value === "RL") {
				return "Agrupará o relatório de comissão por quem lançou a venda no sistema";
			}
			return "";
		} else {
			return "";
		}
	}

	abrirAjuda() {
		window.open(
			"https://pactosolucoes.com.br/ajuda/conhecimento/como-lancar-uma-devolucao-de-cheque-no-sistema-sem-o-financeiro-avancado/",
			"_blank"
		);
	}
}
