<adm-layout
	(goBack)="voltarHome()"
	modulo="Administrativo"
	pageTitle="Comissão para Consultor">
	<pacto-cat-card-plain>
		<form [formGroup]="formGroup">
			<div class="row">
				<!-- Seleção de Empresa (apenas para administradores) -->
				<div class="col-md-6 margin" *ngIf="isAdministrador">
					<div class="label-column">
						<ds3-field-label>Empresa:</ds3-field-label>
					</div>
					<div class="input-column">
						<ds3-select
							[options]="empresas"
							[valueKey]="'codigo'"
							[nameKey]="'nome'"
							[placeholder]="'Selecione a empresa'"
							formControlName="empresa"
							ds3Input></ds3-select>
					</div>
				</div>

				<!-- Tipo dos Dados (obrigatório) -->
				<div class="col-md-12">
					<ds3-form-field>
						<ds3-field-label>Tipo dos dados</ds3-field-label>
						<ds3-select
							id="select-tipo-dados"
							[options]="tiposDados"
							[valueKey]="'value'"
							[nameKey]="'label'"
							[formControl]="formGroup.get('tipoRelatorioEscolhido')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>

				<!-- Configuração de Período -->
				<div class="col-md-6 margin" *ngIf="showPeriodo">
					<ds3-form-field>
						<ds3-field-label>Período</ds3-field-label>
						<ds3-input-date
							[position]="'middle-right'"
							ds3Input
							dateType="dateranger"
							[controlStart]="formGroup.get('dataInicio')"
							[controlEnd]="formGroup.get('dataFinal')"></ds3-input-date>
					</ds3-form-field>
				</div>

				<!-- Competência -->
				<div class="col-md-6 margin" *ngIf="showCompetencia">
					<ds3-form-field>
						<ds3-field-label>Competência</ds3-field-label>
						<ds3-input-date
							ds3Input
							[control]="formGroup.get('competencia')"></ds3-input-date>
					</ds3-form-field>
				</div>

				<!-- Somente recebimento a partir -->
				<div class="col-md-6 margin" *ngIf="showRecebimentoAPartir">
					<ds3-form-field>
						<ds3-field-label>Somente recebimento a partir</ds3-field-label>
						<ds3-input-date
							ds3Input
							[control]="formGroup.get('dataInicioR')"></ds3-input-date>
					</ds3-form-field>
				</div>

				<!-- Somente contratos lançados a partir -->
				<div class="col-md-6 margin" *ngIf="showContratosLancadosAPartir">
					<ds3-form-field>
						<ds3-field-label>Somente contratos lançados a partir</ds3-field-label>
						<ds3-input-date
							ds3Input
							[control]="formGroup.get('dataContratosLancadosAPartir')"></ds3-input-date>
					</ds3-form-field>
				</div>

				<!-- Operador Responsável		 -->
				<div class="col-md-6 margin" *ngIf="showResponsavelLancamento || showOperadorResponsavel">
					<div class="div-operador-resp">
						<ds3-form-field *ngIf="showOperadorResponsavel">
							<ds3-field-label>Operador Responsável</ds3-field-label>
							<ds3-select
								id="select-operador"
								[options]="operadoresResponsaveis"
								[valueKey]="'value'"
								[nameKey]="'label'"
								[formControl]="formGroup.get('tipoResponsavel')"
								ds3Input></ds3-select>
						</ds3-form-field>
						<ds3-form-field>
							<ds3-field-label *ngIf="showResponsavelLancamento">Responsável Lançamento</ds3-field-label>
							<ds3-field-label *ngIf="!showResponsavelLancamento">Atendente</ds3-field-label>
							<ds3-select
								id="select-atendente"
								[options]="colaboradores"
								[valueKey]="'codigo'"
								[nameKey]="'nomeApresentar'"
								[formControl]="formGroup.get('atendente')"
								ds3Input></ds3-select>
						</ds3-form-field>
					</div>
				</div>

				<!-- Nome do consultor -->
				<div class="col-md-6 margin">
					<ds3-form-field>
						<ds3-field-label>Consultor</ds3-field-label>
						<ds3-select
							id="select-consultores"
							[options]="consultores"
							[valueKey]="'codigo'"
							[nameKey]="'nomeApresentar'"
							[formControl]="formGroup.get('consultor')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>

				<!-- Duração -->
				<div class="col-md-6 margin">
					<ds3-form-field>
						<ds3-field-label>Duração</ds3-field-label>
						<ds3-select-multi
							id="select-duracoes"
							[options]="duracoes"
							[valueKey]="'numeroMeses'"
							[nameKey]="'descricao'"
							[formControl]="formGroup.get('duracoes')"
							ds3Input></ds3-select-multi>
					</ds3-form-field>
				</div>

				<!-- Tipo do pagamento -->
				<div class="col-md-6 margin">
					<ds3-form-field>
						<ds3-field-label>Tipo do pagamento</ds3-field-label>
						<ds3-select-multi
							id="select-tipo-pagamento"
							[options]="tiposPagamento"
							[valueKey]="'value'"
							[nameKey]="'label'"
							[formControl]="formGroup.get('tipoContrato')"
							ds3Input></ds3-select-multi>
					</ds3-form-field>
				</div>

				<!-- Impressão por -->
				<div class="col-md-6 margin">
					<ds3-form-field>
						<ds3-field-label>Impressão por</ds3-field-label>
						<ds3-select
							id="select-tipo-impressao"
							[options]="impressoesPor"
							[valueKey]="'value'"
							[nameKey]="'label'"
							[formControl]="formGroup.get('opcaoImpressao')"
							ds3Input></ds3-select>
					</ds3-form-field>
					<div class="text-info"
							 *ngIf="msgImpressao.length > 0">
						{{ msgImpressao }}
					</div>
				</div>

				<!-- Modo de visualização -->
				<div class="col-md-6 margin">
					<ds3-form-field>
						<ds3-field-label>Modo de visualização</ds3-field-label>
						<ds3-select
							id="select-modo-visualizacao"
							[options]="modosVisualizacao"
							[valueKey]="'value'"
							[nameKey]="'label'"
							[formControl]="formGroup.get('visualizacao')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>

				<!-- Valor de Comissões -->
				<div class="col-md-6 margin">
					<ds3-form-field>
						<ds3-field-label>Valor de comissões</ds3-field-label>
						<ds3-select
							id="select-valor-comissao"
							[options]="valoresComissoes"
							[valueKey]="'value'"
							[nameKey]="'label'"
							[formControl]="formGroup.get('tipoValorComissoes')"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>

				<!-- Opções Avançadas -->
				<div class="col-md-6 margin">
					<div *ngIf="showConsiderarDataCompensacao">
						<ds3-form-field>
							<ds3-checkbox ds3Input [formControl]="formGroup.get('considerarCompensacaoOriginal')">
								Considerar data de compensação original
							</ds3-checkbox>
						</ds3-form-field>
					</div>

					<div *ngIf="showChequesEmPendencia && false"
							 class="div-desconsiderar-cheques-pendencia"
							 [pactoCatTolltip]="desconsiderarChequesPendencia"
							 position="top"
							 [darkTheme]="true">
						<ds3-form-field>
							<ds3-checkbox ds3Input
														disabled="true"
														[formControl]="formGroup.get('retirarRecebiveisComPendecia')">
								Desconsiderar cheques que estão na conta do tipo pendência/devolução/custódia no financeiro
							</ds3-checkbox>
						</ds3-form-field>

						<div class="item-click">
							<i class="pct pct-help-circle" style="font-size: 18px"
								 (click)="abrirAjuda()">
							</i>
						</div>

						<ng-template #desconsiderarChequesPendencia>
							<p>
								Este filtro foi desabilitado pois criamos uma forma usual de devolução de cheques e juntamente
								<br /> com ela transformamos este filtro em uma
								configuração presente nas configurações do módulo Financeiro.<br /> Para mais detalhes sobre a nova
								forma de
								devolução de cheques clique em <i class="pct pct-help-circle" style="font-size: 18px"></i>
							</p>
						</ng-template>
					</div>
				</div>

				<!-- Botões de ação -->
				<div class="form-actions">

					<!-- Observação -->
					<div class="div-observacoes-geral">
						<div class="div-observacoes">
							<div class="observacao-text" *ngIf="configuracao?.pagarComissaoSeAtingirMetaFinanceira">
								<i class="pct pct-alert-circle"></i>
								A empresa '{{ nomeEmpresa }}' foi configurada para pagar comissão somente se atingir a Meta Financeira.
								<br />Como a meta financeira leva em consideração o faturamento recebido,
								o cálculo da comissão ficará correto caso seja escolhido o filtro 'Por faturamento recebido'.
							</div>
							<div class="observacao-text"
									 *ngIf="!configuracao?.pagarComissaoProdutos">
								<i class="pct pct-alert-circle"></i>
								Caso queira emitir comissão para produtos, por favor vá nas Configurações de Empresa.
							</div>
							<div class="observacao-text"
									 *ngIf="configuracao?.pagarComissaoProdutos">
								<i class="pct pct-alert-circle"></i>
								Este relatório também apresentará Produtos com Comissão.
							</div>
						</div>
					</div>

					<div class="div-btns-acao">
						<button type="button" ds3-outlined-button (click)="gerarRelatorio('excel')">
							<span>Exportar em Excel</span>
						</button>
						<button type="button" ds3-flat-button (click)="gerarRelatorio('pdf')">
							<span>Gerar relatório em PDF</span>
						</button>
					</div>
				</div>
			</div>
		</form>
	</pacto-cat-card-plain>
</adm-layout>