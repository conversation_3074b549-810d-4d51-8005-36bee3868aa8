@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var";

ds3-form-field {
	display: block;
	margin-bottom: 16px;
}

.row-filters {
	display: grid;
	grid-template-columns: 2fr 1fr 1.5fr 0.75fr 1.5fr 0.75fr auto;
	align-items: end;
	gap: 1rem;
	width: 100%;
	.btn-processar {
		padding-bottom: 16px;
	}
}

.estatisticas-container {
	margin-top: 16px;

	.total-acessos {
		background-color: #ffffff;
		border: 1px solid #d7d8db;
		border-radius: 8px;
		padding: 12px 16px;
		margin-bottom: 16px;

		.label {
			font-weight: 600;
			color: #383b3e;
		}

		.valor {
			font-weight: 700;
			font-size: 18px;
			color: #19a45f;
		}
	}

	.secao-acessos,
	.secao-liberacoes {
		background-color: #ffffff;
		border: 1px solid #d7d8db;
		border-radius: 8px;
		padding: 16px;
		margin-bottom: 16px;

		.titulo-secao {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12px;

			.percentual {
				font-weight: 700;
				font-size: 16px;
				color: #19a45f;
				margin-right: 8px;
			}

			.descricao {
				font-weight: 400;
				color: #383b3e;
			}
		}

		.tabela-acessos,
		.tabela-liberacoes {
			.header-tabela {
				display: grid;
				grid-template-columns: 2fr 1fr 1fr;
				background-color: #f5f5f5;
				padding: 8px 12px;
				border-radius: 4px;
				font-weight: 600;
				color: #383b3e;
				margin-bottom: 4px;
			}

			.linha-tabela {
				display: grid;
				grid-template-columns: 2fr 1fr 1fr;
				padding: 6px 12px;
				border-bottom: 1px solid #e9ecef;

				&:last-child {
					border-bottom: none;
				}
				&.linha-total-bold {
					font-weight: bold;
				}
			}

			.linha-total {
				display: grid;
				grid-template-columns: 2fr 1fr 1fr;
				padding: 8px 12px;
				background-color: #f8f9fa;
				border-top: 2px solid #d7d8db;
				font-weight: 600;
				color: #383b3e;
			}
		}

		.tabela-liberacoes {
			.header-tabela {
				grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
			}

			.linha-tabela {
				grid-template-columns: 2fr 1fr 1fr 1fr 1fr;

				&.linha-total-bold {
					font-weight: bold;
				}
			}

			.linha-total {
				grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
			}
		}
	}

	.secao-justificacao {
		background-color: #ffffff;
		border: 1px solid #d7d8db;
		border-radius: 8px;
		padding: 16px;
		margin-bottom: 16px;

		.status-container {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.status-item {
				text-align: center;

				&.justificado .percentual {
					color: #19a45f;
					font-weight: 700;
					font-size: 18px;
					display: block;
				}

				&.nao-justificado .percentual {
					color: #dc3545;
					font-weight: 700;
					font-size: 18px;
					display: block;
				}

				&.total-pv {
					.label {
						font-weight: 600;
						color: #383b3e;
						margin-right: 8px;
					}

					.valor {
						font-weight: 700;
						font-size: 18px;
						color: #1e60fa;
					}
				}

				.label {
					font-size: 14px;
					color: #80858c;
					margin-top: 4px;
				}
			}
		}
	}
}

.acoes {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	flex-wrap: nowrap;
	gap: 16px;
	margin: 16px 0;

	button > i {
		padding-right: 8px;
	}
}

.legenda-container {
	background-color: #ffffff;
	border: 1px solid #d7d8db;
	border-radius: 8px;
	padding: 16px;
	margin-top: 16px;

	.legenda-header {
		display: flex;
		align-items: flex-start;
		gap: 20px;
	}

	.legenda-section {
		flex: 1;
	}

	.legenda-titulo {
		font-weight: 600;
		font-size: 14px;
		color: #383b3e;
		margin-bottom: 12px;
	}

	.legenda-items {
		display: flex;
		gap: 20px;
		flex-wrap: wrap;

		.legenda-item {
			display: flex;
			align-items: center;
			gap: 8px;

			.cor-indicador {
				width: 16px;
				height: 16px;
				border-radius: 2px;
				display: inline-block;
			}

			.texto {
				font-size: 12px;
				font-weight: 500;
				color: #ffffff;
				padding: 4px 8px;
				border-radius: 4px;
				white-space: nowrap;
			}

			&.ruim {
				.cor-indicador {
					background-color: #dc3545;
				}

				.texto {
					background-color: #dc3545;
				}
			}

			&.regular {
				.cor-indicador {
					background-color: #fd7e14;
				}

				.texto {
					background-color: #fd7e14;
				}
			}

			&.bom {
				.cor-indicador {
					background-color: #28a745;
				}

				.texto {
					background-color: #28a745;
				}
			}

			&.otimo {
				.cor-indicador {
					background-color: #007bff;
				}

				.texto {
					background-color: #007bff;
				}
			}
		}
	}

	.caixa-justificado {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		min-width: 120px;
		padding: 16px;
		border-radius: 8px;
		text-align: center;

		.percentual-justificado {
			font-weight: 700;
			font-size: 24px;
			color: #ffffff;
			line-height: 1;
			margin-bottom: 4px;
		}

		.label-justificado {
			font-size: 12px;
			font-weight: 500;
			color: #ffffff;
			opacity: 0.9;
		}

		&.ruim {
			background-color: #dc3545;
		}

		&.regular {
			background-color: #fd7e14;
		}

		&.bom {
			background-color: #28a745;
		}

		&.otimo {
			background-color: #007bff;
		}
	}
}

.loading-container,
.no-data-container {
	background-color: #ffffff;
	border: 1px solid #d7d8db;
	border-radius: 8px;
	padding: 32px;
	margin-top: 16px;
	text-align: center;

	.loading-message,
	.no-data-message {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 12px;
		font-size: 16px;
		color: #80858c;

		i {
			font-size: 20px;
			color: #1e60fa;
		}
	}

	.loading-message i {
		animation: spin 1s linear infinite;
	}
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
