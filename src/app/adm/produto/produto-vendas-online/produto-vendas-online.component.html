<div *ngIf="produtoFormService.isFormInitialized; else loading">
	<pacto-cat-card-plain
		class="compact-card-padding"
		[formGroup]="produtoFormService.form">
		<div class="compact-form">
			<!-- Seção Configuração Vendas Online -->
			<div class="section-header">
				<h4>Configuração Vendas Online</h4>
			</div>

			<!-- Apresentar no Pacto App -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Apresentar no Pacto App:</label>
				</div>
				<div class="input-column">
					<ds3-checkbox formControlName="apresentarNoPactoApp"></ds3-checkbox>
				</div>
			</div>

			<!-- Apresentar no Vendas Online -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Apresentar no Vendas Online:</label>
				</div>
				<div class="input-column">
					<ds3-checkbox
						formControlName="apresentarNoVendasOnline"></ds3-checkbox>
				</div>
			</div>

			<!-- Apresentar no Pacto Player -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Apresentar no Pacto Player:</label>
				</div>
				<div class="input-column">
					<ds3-checkbox
						formControlName="apresentarNoPactoPlayer"></ds3-checkbox>
				</div>
			</div>

			<!-- Campos condicionais - só aparecem se "Apresentar no Vendas Online" estiver marcado -->
			<div *ngIf="produtoFormService.form.get('apresentarNoVendasOnline')?.value">
				<!-- Quantidade máxima de parcelas -->
				<div class="form-row-centered">
					<div class="label-column">
						<label>Quantidade máxima de parcelas:</label>
					</div>
					<div class="input-column-with-info">
						<ds3-form-field>
							<input
								type="number"
								min="1"
								ds3Input
								formControlName="quantidadeMaximaParcelas" />
						</ds3-form-field>
						<span class="info-text">("A vista" = informe 1)</span>
					</div>
				</div>

				<!-- Seção Imagens -->
				<div class="section-header">
					<h4>Imagens</h4>
				</div>

				<!-- Upload de Imagem -->
				<div class="upload-section">
					<div class="upload-area">
						<input
							type="file"
							id="imageUpload"
							accept="image/*"
							(change)="onUploadImagem($event)"
							style="display: none" />
						<label for="imageUpload" class="upload-label">
							<span>Clique para selecionar uma imagem</span>
						</label>
					</div>
					<button type="button" ds3-flat-button (click)="onAdicionarImagem()">
						Adicionar
					</button>
				</div>

				<!-- Tabela de Imagens -->
				<div class="table-section">
					<div class="table-container">
						<table class="table table-striped">
							<thead>
								<tr>
									<th>Nome</th>
									<th>Arquivo</th>
									<th>Principal</th>
									<th>Ações</th>
								</tr>
							</thead>
							<tbody>
								<tr *ngFor="let imagem of imagens">
									<td>{{ imagem.nome }}</td>
									<td>{{ imagem.arquivo }}</td>
									<td>
										<ds3-checkbox
											[checked]="imagem.principal"
											(change)="onDefinirPrincipal(imagem)"></ds3-checkbox>
									</td>
									<td>
										<div class="action-buttons">
											<button
												type="button"
												ds3-icon-button
												(click)="onEditarImagem(imagem)"
												title="Editar"></button>
											<button
												type="button"
												ds3-icon-button
												(click)="onExcluirImagem(imagem)"
												title="Excluir"></button>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
					</div>

					<!-- Mensagem quando não há dados -->
					<div *ngIf="imagens.length === 0" class="no-data-message">
						<p>Nenhuma imagem adicionada.</p>
					</div>
				</div>

				<!-- Informações adicionais -->
				<div class="info-section">
					<p class="info-note">
						<strong>Nota:</strong>
						As imagens serão exibidas na loja online e no aplicativo. Recomenda-se
						usar imagens com resolução mínima de 800x600 pixels.
					</p>
				</div>
			</div>
		</div>
	</pacto-cat-card-plain>
</div>

<ng-template #loading>
	<div>Carregando formulário...</div>
</ng-template>
