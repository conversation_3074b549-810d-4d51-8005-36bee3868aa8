<div *ngIf="produtoFormService.isFormInitialized">
	<pacto-cat-card-plain
		class="compact-card-padding"
		[formGroup]="produtoFormService.form">
		<!-- Formulário de entrada -->
		<div class="compact-form">
			<!-- Empresa -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Empresa:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="this.produtoFormService.empresas"
							[useFullOption]="true"
							[placeholder]="'Selecione a empresa'"
							[valueKey]="'codigo'"
							[nameKey]="'nome'"
							[(ngModel)]="empresaSelecionada"
							[ngModelOptions]="{ standalone: true }"
							[disabled]="editandoIndex >= 0"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Vigência Inicial -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Vigência Inicial:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-input-date
							ds3Input
							id="vigencia-inicio-comissao"
							[control]="inicio"></ds3-input-date>
					</ds3-form-field>
				</div>
			</div>

			<!-- Vigência Final -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Vigência Final:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-input-date
							ds3Input
							id="vigencia-final-comissao"
							[control]="final"></ds3-input-date>
					</ds3-form-field>
				</div>
			</div>

			<!-- Porcentagem -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Porcentagem (%):</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="text"
							ds3Input
							[(ngModel)]="porcentagem"
							[ngModelOptions]="{ standalone: true }"
							currencyMask
							[options]="{
								prefix: '',
								thousands: '.',
								decimal: ',',
								precision: 2
							}"
							placeholder="0,00" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Valor Fixo -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Valor Fixo (R$):</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input
							type="text"
							ds3Input
							[(ngModel)]="valorFixo"
							[ngModelOptions]="{ standalone: true }"
							currencyMask
							[options]="{ prefix: '', thousands: '.', decimal: ',' }"
							placeholder="0,00" />
					</ds3-form-field>
				</div>
			</div>
		</div>

		<!-- Botões de ação -->
		<div class="button-actions-centered">
			<button
				ds3-flat-button
				type="button"
				(click)="adicionarComissao()"
				[disabled]="!empresaSelecionada">
				{{ editandoIndex >= 0 ? "Atualizar" : "Adicionar" }}
			</button>
			<button
				ds3-outlined-button
				type="button"
				(click)="cancelarEdicao()"
				*ngIf="editandoIndex >= 0">
				Cancelar
			</button>
		</div>
		<!-- Tabela de resultados -->
		<div class="table-section">
			<div class="table-wrapper">
				<table class="table table-bordered">
					<thead>
						<tr>
							<th class="empresa-column">Empresa</th>
							<th class="valor-column">Valor Fixo</th>
							<th class="valor-column">Porcentagem</th>
							<th class="data-column">Vig. Início</th>
							<th class="data-column">Vig. Final</th>
							<th class="opcoes-column">Opções</th>
						</tr>
					</thead>
					<tbody>
						<tr
							*ngFor="let comissao of comissoes; let i = index"
							(click)="editarComissao(i)"
							[class.selected]="editandoIndex === i">
							<td class="empresa-nome">{{ comissao.empresa?.nome || "-" }}</td>
							<td class="valor-cell">
								{{ comissao.valorFixo | currency : "BRL" : "symbol" : "1.2-2" }}
							</td>
							<td class="valor-cell">
								{{ comissao.porcentagem | number : "1.2-2" }}%
							</td>
							<td class="data-cell">
								{{ formatarDataParaExibicao(comissao.vigenciaInicio) }}
							</td>
							<td class="data-cell">
								{{ formatarDataParaExibicao(comissao.vigenciaFinal) }}
							</td>
							<td class="opcoes-cell">
								<i
									class="pct pct-check-circle text-success"
									title="Configurado"></i>
								<i
									class="pct pct-x-circle text-danger"
									title="Remover"
									(click)="removerComissao(i); $event.stopPropagation()"></i>
							</td>
						</tr>
						<tr *ngIf="comissoes.length === 0">
							<td colspan="6" class="text-center text-muted">
								Nenhuma comissão configurada
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</pacto-cat-card-plain>
</div>
