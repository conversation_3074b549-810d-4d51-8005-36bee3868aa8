import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { SnotifyService } from "ng-snotify";
import { ProdutoFormService } from "../produto-form.service";
import { AbstractControl, FormControl } from "@angular/forms";

interface ProdutoComissao {
	codigo?: number;
	produto?: any;
	valorFixo: number;
	porcentagem: number;
	empresa: any;
	vigenciaInicio: string;
	vigenciaFinal: string;
	itemApresentar?: string;
}

@Component({
	selector: "adm-produto-comissao",
	templateUrl: "./produto-comissao.component.html",
	styleUrls: ["./produto-comissao.component.scss"],
})
export class ProdutoComissaoComponent implements OnInit {
	comissoes: ProdutoComissao[] = [];
	empresaSelecionada: any = null;
	porcentagem: number = 0;
	valorFixo: number = 0;

	// Controle de edição
	editandoIndex: number = -1;

	constructor(
		public produtoFormService: ProdutoFormService,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService
	) {}

	ngOnInit(): void {
		this.carregarComissoes();
		this.inicializarFormControls();
	}

	private carregarComissoes(): void {
		this.carregarComissoesDoFormulario();
	}

	adicionarComissao(): void {
		if (!this.empresaSelecionada) {
			this.notificationService.warning("Selecione uma empresa.");
			return;
		}

		const vigenciaInicio = this.produtoFormService.form.get(
			"vigenciaInicioComissao"
		).value;
		const vigenciaFinal = this.produtoFormService.form.get(
			"vigenciaFinalComissao"
		).value;

		if (!vigenciaInicio || !vigenciaFinal) {
			this.notificationService.warning("Preencha as datas de vigência.");
			return;
		}

		if (this.porcentagem <= 0 && this.valorFixo <= 0) {
			this.notificationService.warning("Informe a porcentagem ou valor fixo.");
			return;
		}

		const novaComissao: ProdutoComissao = {
			codigo:
				this.editandoIndex >= 0
					? this.comissoes[this.editandoIndex].codigo
					: null,
			produto: this.criarObjetoProduto(),
			valorFixo: this.valorFixo || 0,
			porcentagem: this.porcentagem || 0,
			empresa: this.empresaSelecionada,
			vigenciaInicio: vigenciaInicio,
			vigenciaFinal: vigenciaFinal,
			itemApresentar: `PRODUTO: ${
				this.produtoFormService.produto.descricao || ""
			}`,
		};

		if (this.editandoIndex >= 0) {
			this.comissoes[this.editandoIndex] = novaComissao;
			this.notificationService.success("Comissão atualizada com sucesso!");
		} else {
			const comissaoExistente = this.comissoes.find(
				(c) =>
					c.empresa.codigo === this.empresaSelecionada.codigo &&
					c.vigenciaInicio === vigenciaInicio &&
					c.vigenciaFinal === vigenciaFinal
			);
			if (comissaoExistente) {
				this.notificationService.warning(
					"Já existe uma comissão para esta empresa neste período."
				);
				return;
			}
			this.comissoes.push(novaComissao);
			this.notificationService.success("Comissão adicionada com sucesso!");
		}
		this.limparFormulario();
		this.salvarComissoesNoFormulario();
	}

	editarComissao(index: number): void {
		const comissao = this.comissoes[index];

		this.empresaSelecionada = this.produtoFormService.empresas.find(
			(e) => e.codigo === comissao.empresa.codigo
		);

		this.produtoFormService.form.patchValue({
			vigenciaInicioComissao: this.formatarDataParaInput(
				comissao.vigenciaInicio
			),
			vigenciaFinalComissao: this.formatarDataParaInput(comissao.vigenciaFinal),
		});

		this.porcentagem = comissao.porcentagem;
		this.valorFixo = comissao.valorFixo;
		this.editandoIndex = index;
	}

	removerComissao(index: number): void {
		const comissao = this.comissoes[index];
		this.comissoes.splice(index, 1);
		this.salvarComissoesNoFormulario();
		this.notificationService.success("Comissão removida com sucesso!");

		if (this.editandoIndex === index) {
			this.cancelarEdicao();
		}
	}

	onConfigurarComissao() {
		const formData = this.produtoFormService.form.value;
		if (
			formData.empresaComercial &&
			formData.vigenciaInicialComercial &&
			formData.vigenciaFinalComercial &&
			(formData.porcentagemComercial > 0 || formData.valorFixoComercial > 0)
		) {
			const empresaSelecionada = this.produtoFormService.empresas.find(
				(emp) => emp.codigo === formData.empresaComercial
			);

			const novaComissao: ProdutoComissao = {
				codigo: null,
				produto: this.criarObjetoProduto(),
				valorFixo: formData.valorFixoComercial || 0,
				porcentagem: formData.porcentagemComercial || 0,
				empresa: empresaSelecionada,
				vigenciaInicio: formData.vigenciaInicialComercial,
				vigenciaFinal: formData.vigenciaFinalComercial,
				itemApresentar: `PRODUTO: ${
					this.produtoFormService.produto.descricao || ""
				}`,
			};

			const comissaoExistente = this.comissoes.find(
				(c) =>
					c.empresa.codigo === empresaSelecionada.codigo &&
					c.vigenciaInicio === formData.vigenciaInicialComercial &&
					c.vigenciaFinal === formData.vigenciaFinalComercial
			);

			if (comissaoExistente) {
				this.notificationService.warning(
					"Já existe uma comissão para esta empresa neste período."
				);
				return;
			}

			this.comissoes.push(novaComissao);
			this.salvarComissoesNoFormulario();
			this.notificationService.success("Comissão configurada com sucesso!");

			this.produtoFormService.form.patchValue({
				empresaComercial: null,
				vigenciaInicialComercial: null,
				vigenciaFinalComercial: null,
				porcentagemComercial: 0,
				valorFixoComercial: 0,
			});
		}
	}

	private limparFormulario(): void {
		this.empresaSelecionada = null;
		this.produtoFormService.form.patchValue({
			vigenciaInicioComissao: null,
			vigenciaFinalComissao: null,
		});
		this.porcentagem = 0;
		this.valorFixo = 0;
		this.editandoIndex = -1;
	}

	private salvarComissoesNoFormulario(): void {
		this.produtoFormService.setComissoesProduto(this.comissoes);
	}

	private carregarComissoesDoFormulario(): void {
		this.produtoFormService.comissoesProduto$.subscribe((value) => {
			const comissoesFormulario = value;
			if (comissoesFormulario && comissoesFormulario.length > 0) {
				this.comissoes = [...comissoesFormulario];
			}
			this.cd.detectChanges();
		});
	}

	private criarObjetoProduto(): any {
		return {
			codigo: this.produtoFormService.produto.codigo,
			descricao: this.produtoFormService.produto.descricao,
			valorFinal: this.produtoFormService.produto.valorFinal,
			desativado: this.produtoFormService.produto.desativado,
			tipoProduto: this.produtoFormService.produto.tipoProduto,
			categoria: this.produtoFormService.produto.categoria,
			tipoProdutoDescricao:
				this.produtoFormService.produto.tipoProdutoDescricao,
			prevalecerVigenciaContrato:
				this.produtoFormService.produto.prevalecerVigenciaContrato,
			bloqueiaPelaVigencia:
				this.produtoFormService.produto.bloqueiaPelaVigencia,
			nrDiasVigencia: this.produtoFormService.produto.nrDiasVigencia,
			pontos: this.produtoFormService.produto.pontos,
			tipoVigencia: this.produtoFormService.produto.tipoVigencia,
			renovavelAutomaticamente:
				this.produtoFormService.produto.renovavelAutomaticamente,
			contratoTextoPadrao: this.produtoFormService.produto.contratoTextoPadrao,
			qtdePontos: this.produtoFormService.produto.qtdePontos,
		};
	}

	private formatarDataParaInput(data: string): string {
		if (!data) {
			return "";
		}
		const date = new Date(data);
		return date.toISOString().split("T")[0];
	}

	formatarDataParaExibicao(data: string): string {
		if (!data) {
			return "";
		}
		const date = new Date(data);
		return date.toLocaleDateString("pt-BR");
	}

	public getComissoes(): ProdutoComissao[] {
		return this.comissoes;
	}

	cancelarEdicao() {
		this.limparFormulario();
	}

	private obterValoresFormControls(): {
		vigenciaInicio: string;
		vigenciaFinal: string;
	} {
		return {
			vigenciaInicio: this.produtoFormService.form.get("vigenciaInicioComissao")
				.value,
			vigenciaFinal: this.produtoFormService.form.get("vigenciaFinalComissao")
				.value,
		};
	}

	private inicializarFormControls(): void {
		this.produtoFormService.form.patchValue({
			vigenciaInicioComissao: null,
			vigenciaFinalComissao: null,
		});
	}

	get inicio(): AbstractControl {
		return this.produtoFormService.form.get("vigenciaInicioComissao");
	}

	get final(): AbstractControl {
		return this.produtoFormService.form.get("vigenciaFinalComissao");
	}
}
