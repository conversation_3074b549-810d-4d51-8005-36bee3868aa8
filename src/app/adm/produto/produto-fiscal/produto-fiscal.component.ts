import { Component, OnInit } from "@angular/core";
import { ProdutoFormService } from "../produto-form.service";

@Component({
	selector: "adm-produto-fiscal",
	templateUrl: "./produto-fiscal.component.html",
	styleUrls: ["./produto-fiscal.component.scss"],
})
export class ProdutoFiscalComponent implements OnInit {
	configuracaoEmissaoNfse = [
		{ codigo: "OPCAO1", descricao: "Opção 1" },
		{ codigo: "OPCAO2", descricao: "Opção 2" },
		{ codigo: "OPCAO3", descricao: "Opção 3" },
	];

	configuracaoEmissaoNfce = [
		{ codigo: "OPCAO1", descricao: "Opção 1" },
		{ codigo: "OPCAO2", descricao: "Opção 2" },
		{ codigo: "OPCAO3", descricao: "Opção 3" },
	];

	constructor(public produtoFormService: ProdutoFormService) {}

	ngOnInit(): void {}
}
