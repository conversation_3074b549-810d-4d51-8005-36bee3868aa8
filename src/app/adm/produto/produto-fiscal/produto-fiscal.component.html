<div *ngIf="produtoFormService.isFormInitialized; else loading">
	<pacto-cat-card-plain
		class="compact-card-padding"
		[formGroup]="produtoFormService.form">
		<div class="compact-form">
			<!-- Seção NFS-e -->
			<div class="section-header">
				<h4>NFS-e</h4>
			</div>

			<!-- Configuração Emissão NFS-e -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Configuração Emissão NFS-e:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="configuracaoEmissaoNfse"
							[useFullOption]="false"
							[placeholder]="'Selecione a configuração'"
							[valueKey]="'codigo'"
							[nameKey]="'descricao'"
							formControlName="configuracaoEmissaoNfse"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Seção NFC-e -->
			<div class="section-header">
				<h4>NFC-e</h4>
			</div>

			<!-- Configuração Emissão NFC-e -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Configuração Emissão NFC-e:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<ds3-select
							[options]="configuracaoEmissaoNfce"
							[useFullOption]="false"
							[placeholder]="'Selecione a configuração'"
							[valueKey]="'codigo'"
							[nameKey]="'descricao'"
							formControlName="configuracaoEmissaoNfce"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<!-- Seção Nota Fiscal -->
			<div class="section-header">
				<h4>Nota Fiscal</h4>
			</div>

			<!-- CFOP -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>CFOP:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="cfop" />
					</ds3-form-field>
				</div>
			</div>

			<!-- NCM -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>NCM:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="ncm" />
					</ds3-form-field>
				</div>
			</div>

			<!-- NCM para NFC-e -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>NCM para NFC-e:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="ncmNfce" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Código Lista Serviço -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Código Lista Serviço:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="codigoListaServico" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Código Tributação Municipal -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Código Tributação Municipal:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="codigoTributacaoMunicipal" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Descrição Serviço Municipal -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Descrição Serviço Municipal:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="descricaoServicoMunicipal" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Enviar Percentual Impostos (Federal, Estadual, Municipal) -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Enviar Percentual Impostos (Federal, Estadual, Municipal):</label>
				</div>
				<div class="input-column">
					<ds3-checkbox formControlName="enviarPercentualImpostos"></ds3-checkbox>
				</div>
			</div>

			<!-- Percentual Federal -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Percentual Federal:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="number" step="0.01" ds3Input formControlName="percentualFederal" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Percentual Estadual -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Percentual Estadual:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="number" step="0.01" ds3Input formControlName="percentualEstadual" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Percentual Municipal -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Percentual Municipal:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="number" step="0.01" ds3Input formControlName="percentualMunicipal" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Seção ICMS -->
			<div class="section-header">
				<h4>ICMS</h4>
			</div>

			<!-- Situação Tributária ICMS -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Situação Tributária ICMS:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="situacaoTributariaIcms" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Alíquota ICMS -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Alíquota ICMS:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="number" step="0.01" ds3Input formControlName="aliquotaIcms" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Seção PIS -->
			<div class="section-header">
				<h4>PIS</h4>
			</div>

			<!-- Situação Tributária PIS -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Situação Tributária PIS:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="situacaoTributariaPis" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Isenção PIS -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Isenção PIS:</label>
				</div>
				<div class="input-column">
					<ds3-checkbox formControlName="isencaoPis"></ds3-checkbox>
				</div>
			</div>

			<!-- Enviar alíquota PIS pro JASON (NFC-e) -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Enviar alíquota PIS pro JASON (NFC-e):</label>
				</div>
				<div class="input-column">
					<ds3-checkbox formControlName="enviarAliquotaPisJason"></ds3-checkbox>
				</div>
			</div>

			<!-- Alíquota PIS -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Alíquota PIS:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="number" step="0.01" ds3Input formControlName="aliquotaPis" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Seção COFINS -->
			<div class="section-header">
				<h4>COFINS</h4>
			</div>

			<!-- Situação Tributária COFINS -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Situação Tributária COFINS:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="text" ds3Input formControlName="situacaoTributariaCofins" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Isenção COFINS -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Isenção COFINS:</label>
				</div>
				<div class="input-column">
					<ds3-checkbox formControlName="isencaoCofins"></ds3-checkbox>
				</div>
			</div>

			<!-- Enviar alíquota COFINS pro JASON (NFC-e) -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Enviar alíquota COFINS pro JASON (NFC-e):</label>
				</div>
				<div class="input-column">
					<ds3-checkbox formControlName="enviarAliquotaCofinsJason"></ds3-checkbox>
				</div>
			</div>

			<!-- Alíquota COFINS -->
			<div class="form-row-centered">
				<div class="label-column">
					<label>Alíquota COFINS:</label>
				</div>
				<div class="input-column">
					<ds3-form-field>
						<input type="number" step="0.01" ds3Input formControlName="aliquotaCofins" />
					</ds3-form-field>
				</div>
			</div>
		</div>
	</pacto-cat-card-plain>
</div>

<ng-template #loading>
	<div>Carregando formulário...</div>
</ng-template>
