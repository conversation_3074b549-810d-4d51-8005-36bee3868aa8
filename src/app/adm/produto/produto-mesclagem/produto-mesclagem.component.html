<div *ngIf="produtoFormService.isFormInitialized; else loading">
	<pacto-cat-card-plain
		class="compact-card-padding"
		[formGroup]="produtoFormService.form">
		<div class="compact-form">
			<!-- Se<PERSON> Mesclagem de produtos -->
			<div class="section-header">
				<h4>Mesclagem de produtos</h4>
			</div>

			<!-- Produto para busca -->
			<div class="search-section">
				<div class="form-row-centered">
					<div class="label-column">
						<label>Produto:</label>
					</div>
					<div class="input-column-with-button">
						<ds3-form-field>
							<ds3-select
								[options]="produtoFormService.tiposProduto"
								[useFullOption]="false"
								[placeholder]="'Selecione o produto'"
								[valueKey]="'codigo'"
								[nameKey]="'descricao'"
								formControlName="produtoMesclagem"
								ds3Input></ds3-select>
						</ds3-form-field>
						<button type="button" ds3-flat-button (click)="onBuscarProduto()">
							Buscar
						</button>
					</div>
				</div>
			</div>

			<!-- Tabela de Produtos Mesclados -->
			<div class="table-section">
				<div class="table-header">
					<h5>Produtos para Mesclagem</h5>
					<button type="button" ds3-flat-button (click)="onAdicionarProduto()">
						Adicionar
					</button>
				</div>

				<div class="table-container">
					<table class="table table-striped">
						<thead>
							<tr>
								<th>Produto</th>
								<th>Valor</th>
								<th>Estoque</th>
								<th>Opções</th>
							</tr>
						</thead>
						<tbody>
							<tr *ngFor="let produto of produtosMesclados">
								<td class="produto-cell">{{ produto.produto }}</td>
								<td class="valor-cell">
									R$ {{ produto.valor | number : "1.2-2" }}
								</td>
								<td class="estoque-cell">{{ produto.estoque }}</td>
								<td>
									<div class="action-buttons">
										<button
											type="button"
											ds3-icon-button
											(click)="onEditarProduto(produto)"
											title="Editar"></button>
										<button
											type="button"
											ds3-icon-button
											(click)="onExcluirProduto(produto)"
											title="Excluir"></button>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>

				<!-- Mensagem quando não há dados -->
				<div *ngIf="produtosMesclados.length === 0" class="no-data-message">
					<p>Nenhum produto adicionado para mesclagem.</p>
				</div>
			</div>

			<!-- Aviso sobre mesclagem -->
			<div class="warning-section">
				<div class="warning-box">
					<span>Atenção! A operação de mesclagem não pode ser desfeita</span>
				</div>
			</div>

			<!-- Botão Salvar produtos mesclados -->
			<div class="save-section">
				<button
					type="button"
					ds3-flat-button
					class="save-button"
					[disabled]="produtosMesclados.length === 0"
					(click)="onSalvarProdutosMesclados()">
					Salvar produtos mesclados
				</button>
			</div>
		</div>
	</pacto-cat-card-plain>
</div>

<ng-template #loading>
	<div>Carregando formulário...</div>
</ng-template>
