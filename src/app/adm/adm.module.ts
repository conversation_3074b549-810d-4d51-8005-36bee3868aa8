import { PlanoEmpresaRedeAcessoComponent } from "@adm/planos/components/plano-empresa-rede-acesso/plano-empresa-rede-acesso.component";
import { PlanoHorarioComponent } from "@adm/planos/components/plano-horario/plano-horario.component";
import { CommonModule } from "@angular/common";
import { HTTP_INTERCEPTORS, HttpClientModule } from "@angular/common/http";
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { MatButtonModule } from "@angular/material/button";
import { MatDialogModule } from "@angular/material/dialog";
import { Router } from "@angular/router";
import { BaseCoreModule } from "@base-core/base-core.module";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { SessionService } from "@base-core/client/session.service";
import { AddIdEmpresaHeadersInterceptor } from "@base-core/rest/add-id-empresa-headers.interceptor";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { AcessoSistemaApiModule } from "acesso-sistema-api";
import { AdmCoreApiModule } from "adm-core-api";
import { AdmLegadoApiModule } from "adm-legado-api";
import { AdmMsApiModule } from "adm-ms-api";
import { BiMsApiModule } from "bi-ms-api";
import { CadastroAuxApiModule } from "cadastro-aux-api";
import { CrmApiModule } from "crm-api";
import { FinanceiroMsApiModule } from "financeiro-ms-api";
import { LoginAppApiModule } from "login-app-api";
import { MarketingApiModule } from "marketing-api";
import { MsPactopayApiModule } from "ms-pactopay-api";
import { SnotifyModule } from "ng-snotify";
import { NgxCurrencyModule } from "ngx-currency";
import { NgxMaskModule } from "ngx-mask";
import { QuillModule } from "ngx-quill";
import { PactoApiModule } from "pacto-api";
import { HomePageModule, PactoLayoutModule } from "pacto-layout";
import { PessoaMsApiModule } from "pessoa-ms-api";
import { PlanoApiModule } from "plano-api";
import { ProdutoApiModule } from "produto-api";
import { RelatorioApiModule } from "relatorio-api";
import { SdkModule } from "sdk";
import { CatTolltipModule, UiModule } from "ui-kit";
import { ZwPactopayApiModule } from "zw-pactopay-api";
import { ZwServletApiModule } from "zw-servlet-api";
import { AdmRoutingModule } from "./adm.routing";
import { FormConfiguracoesBuilderComponent } from "@adm/configuracao/components/inputs/form-configuracoes-builder/form-configuracoes-builder.component";
import { ConfiguracaoModule } from "@adm/configuracao/configuracao.module";
import { ConvenioDescontoFormComponent } from "@adm/convenio-desconto/components/convenio-desconto-form/convenio-desconto-form.component";
import { ConvenioDescontoComponent } from "@adm/convenio-desconto/components/convenio-desconto/convenio-desconto.component";
import { TableConvenioDescontoConfigComponent } from "@adm/convenio-desconto/components/table-convenio-desconto-config/table-convenio-desconto-config.component";
import { DiariasComponent } from "@adm/diarias/components/diarias/diarias.component";
import { HomeComponent } from "@adm/home/<USER>";
import { LayoutModule } from "@adm/layout/layout.module";
import { AddEditModalidadeComponent } from "@adm/modalidade/add-edit-modalidade/add-edit-modalidade.component";
import { ListModalidadeComponent } from "@adm/modalidade/list-modalidade/list-modalidade.component";
import { ModalConfirmExclusaoComponent } from "@adm/modalidade/modal-confirm-exclusao/modal-confirm-exclusao.component";
import { TableProdutoComponent } from "@adm/modalidade/table-produto/table-produto.component";
import { AdmUiModule } from "@adm/modules/adm-ui/adm-ui.module";
import { MyAddAccountComponent } from "@adm/my-add-account/my-add-account.component";
import { ProdutoComponent } from "./produto/produto.component";
import { ProdutoFormComponent } from "./produto/produto-form.component";
import { PacoteFormComponent } from "@adm/pacotes/components/form-pacote/form-pacote.component";
import { ListaPacoteComponent } from "@adm/pacotes/components/lista-pacote/lista-pacote.component";
import { AdmCatTooltipComponent } from "@adm/pacotes/components/tooltip/adm-cat-tooltip.component";
import { CatTooltipTriggerDirective } from "@adm/pacotes/components/tooltip/cat-tooltip-trigger.directive";
import { AddHorarioComponent } from "@adm/planos/components/add-horario/add-horario.component";
import { CadastrarModalidadeHorarioComponent } from "@adm/planos/components/cadastrar-modalidade-horario/cadastrar-modalidade-horario.component";
import { CadastrarModalidadePlanoComponent } from "@adm/planos/components/cadastrar-modalidade-plano/cadastrar-modalidade-plano.component";
import { CadastrarModalidadeRepeticaoComponent } from "@adm/planos/components/cadastrar-modalidade-repeticao/cadastrar-modalidade-repeticao.component";
import { CadastrarPlanoComponent } from "@adm/planos/components/cadastrar-plano/cadastrar-plano.component";
import { TableProdutosComponent } from "@adm/planos/components/cadastrar-plano/lista-produtos/table-produtos.component";
import { PlanoCondicaoPagamentoComponent } from "@adm/planos/components/cadastrar-plano/plano-condicao-pagamento/plano-condicao-pagamento.component";
import { PlanoDadosBasicosComponent } from "@adm/planos/components/cadastrar-plano/plano-dados-basicos/plano-dados-basicos.component";
// tslint:disable-next-line:max-line-length
import { DadosContratuaisConfigAvancadasComponent } from "@adm/planos/components/cadastrar-plano/plano-dados-contratuais/dados-contratuais-config-avancadas/dados-contratuais-config-avancadas.component";
import { PlanoDadosContratuaisComponent } from "@adm/planos/components/cadastrar-plano/plano-dados-contratuais/plano-dados-contratuais.component";
import { TableDuracaoValorComponent } from "@adm/planos/components/cadastrar-plano/table-duracao-valor/table-duracao-valor.component";
import { CategoriaPlanoComponent } from "@adm/planos/components/categoria-plano/categoria-plano.component";
import { CondicaoPagamentoFormComponent } from "@adm/planos/components/condicao-pagamento-form/condicao-pagamento-form.component";
import { CondicaoPagamentoPlanoTableComponent } from "@adm/planos/components/condicao-pagamento-plano-table/condicao-pagamento-plano-table.component";
import { CondicaoPagamentoComponent } from "@adm/planos/components/condicao-pagamento/condicao-pagamento.component";
import { CadastrarPlanoCreditoComponent } from "@adm/planos/components/credito/cadastrar-plano-credito/cadastrar-plano-credito.component";
import { PcCadastroComponent } from "@adm/planos/components/credito/pc-cadastro/pc-cadastro.component";
import { PcCondicaoPagamentoComponent } from "@adm/planos/components/credito/pc-condicao-pagamento/pc-condicao-pagamento.component";
import { PcConfigDadosBasicosComponent } from "@adm/planos/components/credito/pc-config-dados-basicos/pc-config-dados-basicos.component";
import { PcConfigDadosContratuaisComponent } from "@adm/planos/components/credito/pc-config-dados-contratuais/pc-config-dados-contratuais.component";
import { PcDadosBasicosComponent } from "@adm/planos/components/credito/pc-dados-basicos/pc-dados-basicos.component";
import { PcDadosContratualComponent } from "@adm/planos/components/credito/pc-dados-contratual/pc-dados-contratual.component";
import { PcDuracaoValorComponent } from "@adm/planos/components/credito/pc-duracao-valor/pc-duracao-valor.component";
import { PcEditFormComponent } from "@adm/planos/components/credito/pc-edit-form/pc-edit-form.component";
import { PcModalidadesComponent } from "@adm/planos/components/credito/pc-modalidades/pc-modalidades.component";
import { PcProdutosComponent } from "@adm/planos/components/credito/pc-produtos/pc-produtos.component";
import { PcTableHorarioComponent } from "@adm/planos/components/credito/pc-table-horario/pc-table-horario.component";
import { PlanoCreditoDadosContratuaisComponent } from "@adm/planos/components/credito/plano-credito-dados-contratuais/plano-credito-dados-contratuais.component";
import { DescontoFormComponent } from "@adm/planos/components/desconto-form/desconto-form.component";
import { DescontoComponent } from "@adm/planos/components/desconto/desconto.component";
import { DialogConfirmInativarProdutoComponent } from "@adm/planos/components/dialog-confirm-inativar-produto/dialog-confirm-inativar-produto.component";
import { EditFormPlanoComponent } from "@adm/planos/components/edit-form-plano/edit-form-plano.component";
import { HorarioFormComponent } from "@adm/planos/components/horario-form/horario-form.component";
import { HorarioComponent } from "@adm/planos/components/horario/horario.component";
import { PlanosComponent } from "@adm/planos/components/lista-planos/planos.component";
import { PlanoPersonalDadosContratuaisComponent } from "@adm/planos/components/personal/plano-personal-dados-contratuais/plano-personal-dados-contratuais.component";
import { PpCadastroComponent } from "@adm/planos/components/personal/pp-cadastro/pp-cadastro.component";
import { PpDadosBasicosComponent } from "@adm/planos/components/personal/pp-dados-basicos/pp-dados-basicos.component";
import { PpDadosContratuaisComponent } from "@adm/planos/components/personal/pp-dados-contratuais/pp-dados-contratuais.component";
import { PpEditFormComponent } from "@adm/planos/components/personal/pp-edit-form/pp-edit-form.component";
import { ConfigCreditoComponent } from "@adm/planos/components/plano-advanced-config/config-credito/config-credito.component";
import { ConfigNormalComponent } from "@adm/planos/components/plano-advanced-config/config-normal/config-normal.component";
import { ConfigRecorrenciaDadosContratuaisComponent } from "@adm/planos/components/plano-advanced-config/config-recorrencia-dados-contratuais/config-recorrencia-dados-contratuais.component";
import { ConfigRecorrenciaComponent } from "@adm/planos/components/plano-advanced-config/config-recorrencia/config-recorrencia.component";
import { PlanoAdvancedConfigComponent } from "@adm/planos/components/plano-advanced-config/plano-advanced-config.component";
import { ModalCondicaoPagamentoDuracaoComponent } from "@adm/planos/components/plano-duracao/modal-condicao-pagamento-duracao/modal-condicao-pagamento-duracao.component";
import { ModalPlanoDuracaoComponent } from "@adm/planos/components/plano-duracao/modal-plano-duracao/modal-plano-duracao.component";
import { PlanoDuracaoComponent } from "@adm/planos/components/plano-duracao/plano-duracao.component";
import { PlanoEmpresaComponent } from "@adm/planos/components/plano-empresa/plano-empresa.component";
import { ModalVezesSemanaComponent } from "@adm/planos/components/plano-modalidade/modal-vezes-semana/modal-vezes-semana.component";
import { PlanoModalidadeComponent } from "@adm/planos/components/plano-modalidade/plano-modalidade.component";
import { PlanoPacoteComponent } from "@adm/planos/components/plano-pacote/plano-pacote.component";
import { PlanoRecorrenciaDadosContratuaisComponent } from "@adm/planos/components/recorrencia/plano-recorrencia-dados-contraturais/plano-recorrencia-dados-contratuais.component";
import { PrAdvancedConfigDadosContratuaisComponent } from "@adm/planos/components/recorrencia/pr-advanced-config-dados-contratuais/pr-advanced-config-dados-contratuais.component";
import { PrAdvancedConfigComponent } from "@adm/planos/components/recorrencia/pr-advanced-config/pr-advanced-config.component";
import { PrCadastroComponent } from "@adm/planos/components/recorrencia/pr-cadastro/pr-cadastro.component";
import { PrDadosBasicosComponent } from "@adm/planos/components/recorrencia/pr-dados-basicos/pr-dados-basicos.component";
import { PrDadosContratuaisComponent } from "@adm/planos/components/recorrencia/pr-dados-contratuais/pr-dados-contratuais.component";
import { PrEditComponent } from "@adm/planos/components/recorrencia/pr-edit/pr-edit.component";
import { PrTableHorariosComponent } from "@adm/planos/components/recorrencia/pr-table-horarios/pr-table-horarios.component";
import { PrTableModalidadesComponent } from "@adm/planos/components/recorrencia/pr-table-modalidades/pr-table-modalidades.component";
import { PrTableProdutosComponent } from "@adm/planos/components/recorrencia/pr-table-produtos/pr-table-produtos.component";
import { ReplicarPlanoComponent } from "@adm/planos/components/replicar-plano/replicar-plano.component";
import { TipoPlanoFormComponent } from "@adm/planos/components/tipo-plano-form/tipo-plano-form.component";
import { TipoPlanoComponent } from "@adm/planos/components/tipo-plano/tipo-plano.component";
import { PlataformaLinkRedirectComponent } from "@adm/plataforma-link-adm/plataforma-link-redirect.component";
import { FormProdutoComponent } from "@adm/produtos/components/form-produto/form-produto.component";
import { ListaProdutoComponent } from "@adm/produtos/components/lista-produto/lista-produto.component";
import { ProdutosComponent } from "@adm/produtos/components/lista-produto/produtos/produtos.component";
import { TefComponent } from "@adm/tef/tef.component";
import { SafeUrlPipe } from "@adm/util/safeurl.pipe";
import { CaixaOperadorComponent } from "@adm/caixa-operador/caixa-operador.component";
import { CaixaEmAbertoModule } from "@adm/caixa-em-aberto/caixa-em-aberto.module";
import { ModalEnviarLinkPagamentoComponent } from "@adm/negociacao/modal-enviar-link-pagamento/modal-enviar-link-pagamento.component";
import { AmbienteComponent } from "@adm/ambiente/ambiente/ambiente.component";
import { AmbienteFormComponent } from "@adm/ambiente/ambiente-form/ambiente-form.component";
import { TipoModalidadeComponent } from "@adm/tipo-modalidade/tipo-modalidade.component";
import { TipoModalidadeFormComponent } from "@adm/tipo-modalidade/tipo-modalidade-form/tipo-modalidade-form.component";
import { MovimentoProdutoComponent } from "@adm/movimento-produto/components/movimento-produto/movimento-produto.component";
import { AtivarClubeVantagensComponent } from "./ativar-clube-vantagens/ativar-clube-vantagens.component";
import { MovimentoProdutoFormComponent } from "@adm/movimento-produto/components/movimento-produto-form/movimento-produto-form.component";
import { TipoModalidadeModule } from "@adm/tipo-modalidade/tipo-modalidade.module";
import { FormaPagamentoComponent } from "./forma-pagamento/forma-pagamento.component";
import { FormaPagamentoFormComponent } from "./forma-pagamento/forma-pagamento-form/forma-pagamento-form.component";
import { TaxasComissaoComponent } from "./taxas-comissao/taxas-comissao.component";
import { TaxasComissaoFormComponent } from "./taxas-comissao/taxas-comissao-form/taxas-comissao-form.component";
import { ModalNovoLoteCupomComponent } from "@adm/cadastro-auxliar/components/campanha-cupom-desconto/modal-novo-lote-cupom/modal-novo-lote-cupom.component";
import { ProdutoDadosComponent } from "./produto/produto-dados/produto-dados.component";
import { ProdutoComissaoComponent } from "./produto/produto-comissao/produto-comissao.component";
import { ProdutoFiscalComponent } from "./produto/produto-fiscal/produto-fiscal.component";
import { ProdutoVendasOnlineComponent } from "./produto/produto-vendas-online/produto-vendas-online.component";
import { ProdutoMesclagemComponent } from "./produto/produto-mesclagem/produto-mesclagem.component";
import { ProdutoReplicarEmpresasComponent } from "./produto/produto-replicar-empresas/produto-replicar-empresas.component";
import { ProdutoValorEmpresaComponent } from "./produto/produto-valor-empresa/produto-valor-empresa.component";
import { ProdutoValorEmpresaPlanoComponent } from "./produto/produto-valor-empresa-plano/produto-valor-empresa-plano.component";
import { StatusParcelaModule } from "../cobranca/components/status-parcela/status-parcela.module";
import { ProdutoFormService } from "./produto/produto-form.service";
import { FechamentoAcessosComponent } from "./fechamento-acessos/fechamento-acessos.component";
import { ModalListaEmailComponent } from "./fechamento-acessos/modal-lista-email/modal-lista-email.component";
import { DescontoOcupacaoComponent } from "./desconto-ocupacao/desconto-ocupacao.component";
import { PedidosPinpadComponent } from "./pedidos-pinpad/pedidos-pinpad.component";
import { ModalPedidoPinpadParamsComponent } from "./pedidos-pinpad/modal-pedido-pinpad-params/modal-pedido-pinpad-params.component";
import { ModalPedidoPinpadAtualizarComponent } from "./pedidos-pinpad/modal-pedido-pinpad-atualizar/modal-pedido-pinpad-atualizar.component";
import { CompraEstoqueComponent } from "./compra-estoque/compra-estoque.component";
import { CompraEstoqueFormComponent } from "./compra-estoque/compra-estoque-form.component";
import { ModalCompraEstoqueProdutosNaoEncontradoComponent } from "./compra-estoque/modal-compra-estoque-produtos-nao-encontrado/modal-compra-estoque-produtos-nao-encontrado.component";
import { ComissaoConsultorComponent } from "./comissao-consultor/comissao-consultor.component";

@NgModule({
	declarations: [
		HomeComponent,
		PlanosComponent,
		TipoPlanoComponent,
		TipoPlanoFormComponent,
		CadastrarPlanoComponent,
		MyAddAccountComponent,
		TableProdutosComponent,
		CadastrarModalidadePlanoComponent,
		CadastrarModalidadeHorarioComponent,
		CadastrarModalidadeRepeticaoComponent,
		PlanoDadosBasicosComponent,
		PlanoDadosContratuaisComponent,
		DadosContratuaisConfigAvancadasComponent,
		PlanoCondicaoPagamentoComponent,
		EditFormPlanoComponent,
		PlanoAdvancedConfigComponent,
		FormProdutoComponent,
		ListaProdutoComponent,
		TableDuracaoValorComponent,
		ConfigNormalComponent,
		ConfigRecorrenciaComponent,
		ConfigCreditoComponent,
		ConfigRecorrenciaDadosContratuaisComponent,
		PlanoRecorrenciaDadosContratuaisComponent,
		PlanoPersonalDadosContratuaisComponent,
		PlanoCreditoDadosContratuaisComponent,
		PcDadosBasicosComponent,
		CadastrarPlanoCreditoComponent,
		PcModalidadesComponent,
		PcDuracaoValorComponent,
		PcProdutosComponent,
		PcDadosContratualComponent,
		PcCondicaoPagamentoComponent,
		PcEditFormComponent,
		PrCadastroComponent,
		PrDadosBasicosComponent,
		PrTableModalidadesComponent,
		PrTableProdutosComponent,
		PrTableHorariosComponent,
		PrDadosContratuaisComponent,
		PrAdvancedConfigComponent,
		PrAdvancedConfigDadosContratuaisComponent,
		PrEditComponent,
		PcCadastroComponent,
		PcTableHorarioComponent,
		PcConfigDadosBasicosComponent,
		PcConfigDadosContratuaisComponent,
		PlanoEmpresaComponent,
		PpCadastroComponent,
		PpDadosBasicosComponent,
		PpDadosContratuaisComponent,
		PpEditFormComponent,
		TipoPlanoFormComponent,
		PlataformaLinkRedirectComponent,
		DescontoFormComponent,
		DescontoComponent,
		ListaPacoteComponent,
		PacoteFormComponent,
		ProdutosComponent,
		ConvenioDescontoComponent,
		ConvenioDescontoFormComponent,
		TableConvenioDescontoConfigComponent,
		CondicaoPagamentoComponent,
		CondicaoPagamentoPlanoTableComponent,
		CondicaoPagamentoFormComponent,
		ReplicarPlanoComponent,
		DialogConfirmInativarProdutoComponent,
		HorarioComponent,
		HorarioFormComponent,
		AddHorarioComponent,
		DiariasComponent,
		MovimentoProdutoComponent,
		FormaPagamentoComponent,
		FormaPagamentoFormComponent,
		TaxasComissaoComponent,
		TaxasComissaoFormComponent,
		MovimentoProdutoFormComponent,
		AdmCatTooltipComponent,
		CatTooltipTriggerDirective,
		SafeUrlPipe,
		PlanoPacoteComponent,
		PlanoModalidadeComponent,
		ModalVezesSemanaComponent,
		CategoriaPlanoComponent,
		PlanoDuracaoComponent,
		ModalPlanoDuracaoComponent,
		ModalCondicaoPagamentoDuracaoComponent,
		AddEditModalidadeComponent,
		ListModalidadeComponent,
		TableProdutoComponent,
		ModalConfirmExclusaoComponent,
		PlanoHorarioComponent,
		PlanoEmpresaRedeAcessoComponent,
		CaixaOperadorComponent,
		ModalEnviarLinkPagamentoComponent,
		TefComponent,
		ModalEnviarLinkPagamentoComponent,
		AmbienteComponent,
		AmbienteFormComponent,
		AtivarClubeVantagensComponent,
		ModalNovoLoteCupomComponent,
		ProdutoComponent,
		ProdutoFormComponent,
		ProdutoDadosComponent,
		ProdutoComissaoComponent,
		ProdutoFiscalComponent,
		ProdutoVendasOnlineComponent,
		ProdutoMesclagemComponent,
		ProdutoReplicarEmpresasComponent,
		ProdutoValorEmpresaComponent,
		ProdutoValorEmpresaPlanoComponent,
		FechamentoAcessosComponent,
		ModalListaEmailComponent,
		DescontoOcupacaoComponent,
		PedidosPinpadComponent,
		ModalPedidoPinpadParamsComponent,
		ModalPedidoPinpadAtualizarComponent,
		CompraEstoqueComponent,
		CompraEstoqueFormComponent,
		ModalCompraEstoqueProdutosNaoEncontradoComponent,
		ComissaoConsultorComponent,
	],
	entryComponents: [
		CadastrarModalidadePlanoComponent,
		CadastrarModalidadeHorarioComponent,
		CadastrarModalidadeRepeticaoComponent,
		PlanoAdvancedConfigComponent,
		PrAdvancedConfigComponent,
		PrAdvancedConfigDadosContratuaisComponent,
		PcConfigDadosBasicosComponent,
		PcConfigDadosContratuaisComponent,
		DialogConfirmInativarProdutoComponent,
		AddHorarioComponent,
		FormConfiguracoesBuilderComponent,
		ModalVezesSemanaComponent,
		ModalPlanoDuracaoComponent,
		ModalCondicaoPagamentoDuracaoComponent,
		ModalConfirmExclusaoComponent,
		ModalEnviarLinkPagamentoComponent,
		TefComponent,
		ModalEnviarLinkPagamentoComponent,
		ModalNovoLoteCupomComponent,
		ModalListaEmailComponent,
		ModalPedidoPinpadParamsComponent,
		ModalPedidoPinpadAtualizarComponent,
	],
	imports: [
		AdmRoutingModule,
		CommonModule,
		BaseSharedModule,
		HomePageModule,
		NgbModule,
		HttpClientModule,
		NgxMaskModule.forRoot(),
		SdkModule,
		ReactiveFormsModule,
		FormsModule,
		LayoutModule,
		SnotifyModule,
		BaseCoreModule,
		AcessoSistemaApiModule,
		BiMsApiModule,
		CadastroAuxApiModule,
		PessoaMsApiModule,
		PlanoApiModule,
		ProdutoApiModule,
		RelatorioApiModule,
		AdmCoreApiModule,
		MsPactopayApiModule,
		AdmUiModule,
		UiModule,
		PactoApiModule,
		ZwPactopayApiModule,
		AdmMsApiModule,
		LoginAppApiModule,
		MarketingApiModule,
		CrmApiModule,
		FinanceiroMsApiModule,
		ZwServletApiModule,
		ConfiguracaoModule,
		QuillModule,
		NgxCurrencyModule,
		CaixaEmAbertoModule,
		MatDialogModule,
		MatButtonModule,
		TipoModalidadeModule,
		StatusParcelaModule,
		AdmLegadoApiModule,
		CatTolltipModule,
	],
	providers: [
		{
			provide: HTTP_INTERCEPTORS,
			useClass: AddIdEmpresaHeadersInterceptor,
			multi: true,
		},
		ProdutoFormService,
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AdmModule {
	constructor(private router: Router, private session: SessionService) {}
}
