import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { AulaExcluidaListaComponent } from "./components/aula-excluida-lista/aula-excluida-lista.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

const funcionalidades = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.EXCLUIR_AULAS_DIA,
	true
);

const routes: Routes = [
	{
		path: "",
		component: AulaExcluidaListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes), CommonModule, BaseSharedModule],
	declarations: [AulaExcluidaListaComponent],
})
export class AulaExcluidaModule {}
