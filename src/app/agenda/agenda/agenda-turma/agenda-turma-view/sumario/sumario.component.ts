import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Input,
	ViewChild,
} from "@angular/core";
import { TurmaZW, TreinoApiTurmaService } from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { TurmaRemoverAulaCheiaModalComponent } from "../../turma-remover-aula-cheia-modal/turma-remover-aula-cheia-modal.component";
import { ModalService } from "@base-core/modal/modal.service";
import { AgendaTurmaStateService } from "../../../services/agenda-turma-state.service";
import { SnotifyService } from "ng-snotify";
import { TraducoesXinglingComponent } from "ui-kit";
import { PerfilAcessoFuncionalidadeNome } from "treino-api";
import { TurmaSubstituirProfessorModalComponent } from "../../turma-substituir-professor-modal/turma-substituir-professor-modal.component";
import { TreinoApiColaboradorService } from "treino-api";
import { Router } from "@angular/router";
import { RestService } from "@base-core/rest/rest.service";

declare var moment;

@Component({
	selector: "pacto-sumario",
	templateUrl: "./sumario.component.html",
	styleUrls: ["./sumario.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SumarioComponent implements OnInit {
	@Input() turma: TurmaZW;
	@ViewChild("mensagemNotificacao", { static: true })
	mensagemNotificacao: TraducoesXinglingComponent;
	integracaoZW = false;
	podeExcluirAulaCheia = false;
	podeSubstituirProfesor = false;

	constructor(
		private sessionService: SessionService,
		private modalService: ModalService,
		private turmaService: TreinoApiTurmaService,
		private rest: RestService,
		private turmaState: AgendaTurmaStateService,
		private snotifyService: SnotifyService,
		private colaboradorService: TreinoApiColaboradorService,
		private router: Router
	) {}

	get dia() {
		return moment(this.turma.dia).toDate();
	}

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.podeExcluirAulaCheia = this.permitirExcluirAulaCheia();
		this.podeSubstituirProfesor = this.permitirSubstituirProfessor();
	}

	removeHandler() {
		if (this.turma.numeroAlunos === 0) {
			const modal = this.modalService.open(
				"Excluir aula",
				TurmaRemoverAulaCheiaModalComponent
			);
			modal.result.then((result) => {
				this.turmaService
					.excluirAulaCheia(this.turma.horarioTurmaId, this.turma.dia, result)
					.subscribe(() => {
						this.snotifyService.success(
							this.mensagemNotificacao.getLabel("aulaCheiaExcluida")
						);
						this.router.navigate(["agenda", "painel", "turmas"]);
						this.turmaState.forceLoad$.next(true);
					});
			});
		} else {
			this.snotifyService.error(
				this.mensagemNotificacao.getLabel("existeAlunosNaAula")
			);
		}
	}

	substituirProfessor() {
		if (this.podeSubstituirProfesor && this.turma && this.turma.aulaCheia) {
			this.colaboradorService
				.obterTodosColaboradoresAptosAAula(false)
				.subscribe((response) => {
					const modal = this.modalService.open(
						this.mensagemNotificacao.getLabel("substituirProfessor"),
						TurmaSubstituirProfessorModalComponent
					);
					modal.componentInstance.professores = response.content;
					modal.result.then((result) => {
						this.turmaService
							.substituirProfessor(
								this.turma.horarioTurmaId,
								this.turma.dia,
								result
							)
							.subscribe(() => {
								this.snotifyService.success(
									this.mensagemNotificacao.getLabel("substituirProfessorSucess")
								);
								this.turmaService
									.aulaDetalhada(this.turma.horarioTurmaId, this.turma.dia)
									.subscribe((turma) => {
										this.turmaState.updateTurma(turma);
										this.turmaState.stopLoad();
									});
							});
					});
				});
		}
	}

	private permitirExcluirAulaCheia(): boolean {
		return this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.EXCLUIR_AULAS_DIA
		);
	}

	private permitirSubstituirProfessor(): boolean {
		const horarioInicio = this.turma.horarioInicio.split(":");
		const dataTurma = moment(this.turma.dia)
			.add(horarioInicio[0], "hours")
			.add(horarioInicio[1], "minutes")
			.toDate();
		const dataHoje = new Date();
		return (
			(moment(dataHoje).isBefore(dataTurma) &&
				this.sessionService.funcionalidades.get(
					PerfilAcessoFuncionalidadeNome.ALTERAR_PROFESSOR_AULA
				)) ||
			((moment(dataHoje).isAfter(dataTurma) || dataHoje === dataTurma) &&
				this.sessionService.funcionalidades.get(
					PerfilAcessoFuncionalidadeNome.ALTERAR_PROFESSOR_AULA_INICIADA
				))
		);
	}

	get urlLog() {
		return this.rest.buildFullUrl(
			`log/aula/${this.turma.horarioTurmaId}/${this.turma.dia}`
		);
	}
}
