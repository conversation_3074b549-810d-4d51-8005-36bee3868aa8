.block-info {
	padding: 15px 30px 15px 30px;
	text-align: center;

	pacto-cat-person-avatar {
		left: 40%;
		right: 40%;
		position: relative;
	}

	.buttons-container {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 16px;

		&.single-button {
			grid-template-columns: 1fr;
		}
	}
}

.alert-message {
	display: flex;
	align-items: flex-start;
	gap: 8px;
	padding: 12px;
	margin: 15px 30px;
	background-color: #fff3cd;
	border: 1px solid #ffeaa7;
	border-radius: 4px;
	text-align: left;

	i {
		color: #856404;
		font-size: 16px;
		margin-top: 2px;
		flex-shrink: 0;
	}

	span {
		color: #856404;
		font-size: 14px;
		line-height: 1.4;
	}
}
