<pacto-relatorio
	[customEmptyContent]="customEmptyDisponibilidade"
	[enableZebraStyle]="true"
	[showShare]="false"
	[table]="table"></pacto-relatorio>

<ng-template #customEmptyDisponibilidade>
	<div class="empty-turma">
		<img src="pacto-ui/images/empty-state-turma.svg" />
		<span class="titulo">Lista de disponibilidades vazia</span>
		<span>Ainda não possui nenhuma disponibilidade cadastrada</span>
	</div>
</ng-template>

<ng-template #nomeColumn let-item="item">
	<span class="professor">
		{{ locacao ? item.descricao.toLowerCase() : item.tipo.nome.toLowerCase() }}
	</span>
</ng-template>

<ng-template #comportamentoColumn let-item="item">
	<span class="professor">
		{{ item.tipo.comportamentoNome.toLowerCase() }}
	</span>
</ng-template>

<ng-template #professorColumn let-item="item">
	<span class="professor">
		{{ item.professor.nome.toLowerCase() }}
	</span>
</ng-template>

<ng-template #horarioColumn let-item="item">
	<span>{{ item.horarioInicial }} às {{ item.horarioFinal }}</span>
</ng-template>

<ng-template #acoesColumn let-item="item">
	<div *ngIf="!item.bloqueado">
		<span (click)="agendar(item)" class="agendar">Agendar</span>
	</div>
	<div *ngIf="item.bloqueado">
		<span class="agendar">
			<i
				[ngbPopover]="popoverAmbienteBloqueadoLocacao"
				class="pct pct-alert-circle"
				triggers="mouseenter:mouseleave"></i>
		</span>
	</div>
</ng-template>

<ng-template #popoverAmbienteBloqueadoLocacao>
	<div class="popover-ambiente-bloqueado-locacao">
		Você não pode reservar esta locação porque o ambiente onde ela se realizaria
		já se encontra ocupado.
	</div>
</ng-template>
