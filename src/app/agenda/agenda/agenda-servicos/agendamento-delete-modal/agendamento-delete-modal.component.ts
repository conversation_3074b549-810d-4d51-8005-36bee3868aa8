import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Input,
	<PERSON><PERSON><PERSON>roy,
	OnInit,
} from "@angular/core";
import { AgendaAgendamento, AgendaAgendamentoStatus } from "treino-api";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AgendaUtilsService } from "@base-core/agenda/agenda-utils.service";

declare var moment;

@Component({
	selector: "pacto-agendamento-delete-modal",
	templateUrl: "./agendamento-delete-modal.component.html",
	styleUrls: ["./agendamento-delete-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendamentoDeleteModalComponent implements OnInit, OnDestroy {
	@Input() agendamento: AgendaAgendamento;

	agendamentoFg = new FormGroup({
		dia: new FormControl(),
		duracao: new FormControl(),
		horarioInicial: new FormControl(),
		horarioFinal: new FormControl(),
		observacao: new FormControl(),
	});

	showObservacao = false;

	get AgendaAgendamentoStatus() {
		return AgendaAgendamentoStatus;
	}

	get alunoUrl() {
		const uri = this.agendamento.aluno.imageUri;
		return uri ? `url(${uri})` : null;
	}

	get professorUrl() {
		const uri = this.agendamento.professor.imageUri;
		return uri ? `url(${uri})` : null;
	}

	ngOnInit() {
		this.showObservacao = this.agendamento.observacao ? true : false;
		this.agendamentoFg.patchValue({
			dia: this.agendamento.data,
			horarioInicial: this.agendamento.horarioInicial,
			horarioFinal: this.agendamento.horarioFinal,
			observacao: this.agendamento.observacao,
			duracao: this.getDuracao(),
		});
		this.agendamentoFg.get("horarioInicial").disable({ emitEvent: false });
		this.agendamentoFg.get("horarioFinal").disable({ emitEvent: false });
		this.agendamentoFg.get("duracao").disable({ emitEvent: false });
		this.agendamentoFg.get("dia").disable({ emitEvent: false });
	}

	ngOnDestroy() {}

	private getDuracao() {
		const finalMinutos = this.agendaUtil.convertTimeLabelIntoMinutes(
			this.agendamento.horarioFinal
		);
		const inicioMinutos = this.agendaUtil.convertTimeLabelIntoMinutes(
			this.agendamento.horarioInicial
		);
		return finalMinutos - inicioMinutos;
	}

	constructor(
		private cd: ChangeDetectorRef,
		private openModal: NgbActiveModal,
		private agendaUtil: AgendaUtilsService
	) {}

	adicionarObsHandler() {
		this.showObservacao = true;
	}

	cancelarHandler() {
		this.openModal.dismiss();
	}

	salvarHandler() {
		this.openModal.close(this.agendamentoFg.get("observacao").value);
	}
}
