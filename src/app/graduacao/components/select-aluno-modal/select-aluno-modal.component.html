<div [maxHeight]="'400px'" class="scroll-aux" pactoCatSmoothScroll>
	<table>
		<thead>
			<th>Nome</th>
			<th>Matrícula</th>
			<th class="selecionar">
				<div class="wrapper">
					<pacto-cat-checkbox
						[control]="selectAllFc"
						[label]="''"></pacto-cat-checkbox>
					<div>Todos</div>
				</div>
			</th>
		</thead>
		<tbody>
			<tr *ngFor="let aluno of alunos">
				<td>{{ aluno.nome }}</td>
				<td>{{ aluno.matricula }}</td>
				<td>
					<div>
						<pacto-cat-checkbox
							[control]="alunosFg.get(aluno.id.toString())"
							[label]="''"></pacto-cat-checkbox>
					</div>
				</td>
			</tr>
		</tbody>
	</table>
</div>

<div class="footer">
	<div class="total-number">{{ alunos.length }} alunos(s)</div>
	<pacto-cat-button
		(click)="confirmarHandler()"
		[icon]="'user-plus'"
		[label]="'confirmar'"></pacto-cat-button>
</div>
