@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	padding: 15px 30px 30px 30px;
}

table {
	width: 100%;

	th {
		@extend .type-btn-bold;
		text-transform: uppercase;
		padding-bottom: 15px;

		&.selecionar {
			width: 100px;
		}

		&.selecionar .wrapper {
			display: flex;
			cursor: pointer;
		}

		pacto-cat-checkbox {
			display: block;
			width: 25px;
			position: relative;
			top: -4px;
		}
	}

	tr {
		&:hover {
			background-color: $cinza01;
			cursor: pointer;
		}
	}

	td {
		@extend .type-caption-rounded;
		text-transform: uppercase;
		line-height: 48px;
		color: $cinza03;
		border-bottom: 1px solid $cinza01;

		&:first-child() {
			padding-left: 15px;
		}

		pacto-cat-checkbox {
			position: relative;
			top: 10px;
		}
	}
}

.footer {
	margin-top: 15px;
	line-height: 32px;
	display: flex;
	justify-content: space-between;

	.total-number {
		@extend .type-p-small;
	}
}
