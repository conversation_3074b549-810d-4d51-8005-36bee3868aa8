<div class="div-atividade-geral">
	<div class="upper">
		<img class="atividade-avatar" src="{{ uri }}" />
		<div class="info">
			<div class="type-h4">{{ name }}</div>
			<div class="description type-p-small-rounded">{{ description }}</div>
		</div>
	</div>

	<div *ngIf="possuiSubAtividade" class="div-sub-atividades">
		<div>
			<div class="row">
				<div class="column-head-nome col-md-4">
					<div class="title-sub-atividade">SubAtividades</div>
				</div>
				<div class="column-head-resposta col-md-4">
					<div class="title-sub-atividade">Resposta</div>
				</div>
				<div class="column-head-observacao col-md-4">
					<div>Observação</div>
				</div>
			</div>

			<div *ngFor="let subAtividade of subAtividadesControl; let index = index">
				<div
					*ngFor="let avaliado of avaliadosSubAtividadeControl(subAtividade)">
					<div class="row row-aluno">
						<div class="column-row-aluno col-md-4">
							<div class="celula-nome-aluno">
								{{
									getSubAtividadePorId(subAtividade.get("subAtividadeId").value)
										.nome
								}}
							</div>
						</div>
						<div class="column-row-resposta col-md-3">
							<div
								*ngIf="criterioSimNao"
								[ngClass]="{ disabled: disabled(avaliado) }"
								class="options">
								<div
									(click)="selectOptionHandler(index, avaliado)"
									*ngFor="let option of answerOptions; let index = index"
									[style.flex-basis.%]="100 / answerOptions.length"
									class="option type-h6 {{
										verificarResposta(index, avaliado)
									}}">
									<div class="option-lbl">
										{{ option }}
									</div>
								</div>
							</div>
							<pacto-cat-select
								*ngIf="!criterioSimNao"
								[control]="avaliado.get('resposta')"
								[id]="'select-status'"
								[items]="answerOptions"></pacto-cat-select>
						</div>
						<div class="column-row-observacao col-md-5">
							<div class="">
								<input
									[formControl]="avaliado.get('observacao')"
									class="pacto-input type-p-small-rounded"
									type="text" />
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div *ngIf="!possuiSubAtividade" class="div-sub-atividades">
		<div>
			<div class="row">
				<div class="column-head-resposta col-md-6">
					<div class="title-sub-atividade">Resposta</div>
				</div>
				<div class="column-head-observacao col-md-6">
					<div>Observação</div>
				</div>
			</div>

			<div *ngFor="let avaliado of avaliadosControl">
				<div class="row row-aluno">
					<div class="column-row-resposta col-md-6">
						<div
							*ngIf="criterioSimNao"
							[ngClass]="{ disabled: disabled(avaliado) }"
							class="options">
							<div
								(click)="selectOptionHandler(index, avaliado)"
								*ngFor="let option of answerOptions; let index = index"
								[style.flex-basis.%]="100 / answerOptions.length"
								class="option type-h6 {{ verificarResposta(index, avaliado) }}">
								<div class="option-lbl">
									{{ option }}
								</div>
							</div>
						</div>
						<pacto-cat-select
							*ngIf="!criterioSimNao"
							[control]="avaliado.get('resposta')"
							[id]="'select-status'"
							[items]="answerOptions"></pacto-cat-select>
					</div>
					<div class="column-row-observacao col-md-6">
						<div class="">
							<input
								[formControl]="avaliado.get('observacao')"
								class="pacto-input type-p-small-rounded"
								type="text" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
