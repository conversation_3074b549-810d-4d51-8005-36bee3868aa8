@import "projects/ui/assets/import.scss";

.cardAluno {
	display: inline-block;
}

.div-button {
	float: right;
	margin-top: 30px;
	margin-right: 30px;

	pacto-cat-button {
		margin-left: 10px;
	}
}

.div-button-mobile {
	display: none;
}

.excluir-selecionados {
	background: $hellboyPri;
	color: $branco;
}

.primary-cor {
	background: $azulimPri;
}

pacto-cat-person-avatar {
	vertical-align: middle;
	display: inline-block;
}

.nome-aluno {
	margin-left: 15px;
	display: inline-block;
}

::ng-deep.pct-trash-2 {
	color: $hellboyPri;
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	.div-listagem-alunos {
		.div-lista-alunos {
			::ng-deep.pacto-table-title-block {
				padding: 15px 5px;

				.command-buttons {
					justify-content: space-between;
					width: 100%;

					.command {
						width: 50%;

						.icone {
							margin-right: 1px;
							margin-left: -2px;
						}

						.texto {
							font-size: 10px;
						}
					}

					.command:nth-child(2) {
						margin: 0;
					}
				}
			}

			::ng-deep.table-content {
				overflow: auto;

				.table {
					width: 1000px;
					max-width: 1000px;
				}
			}
		}

		.div-button {
			display: none;
		}

		.div-button-mobile {
			display: block;
			padding: 12px;

			::ng-deep pacto-cat-button .pacto-button {
				width: 100%;
				margin-top: 15px;
			}
		}
	}
	::ng-deep .modal-dialog {
		.pacto-table-title-block {
			padding: 10px 12px;

			.command-buttons {
				.command {
					.texto {
						font-size: 10px;
					}
				}
			}
		}
	}

	::ng-deep .table-content {
		overflow: auto;

		.table {
			width: 1000px;
			max-width: 1000px;
		}
	}
}
