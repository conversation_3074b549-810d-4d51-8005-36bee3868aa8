@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	width: 100%;
	height: 100vh;
	display: block;
}

.tv-aula-header {
	height: 112px;
	background-color: $azulPacto05;
	display: flex;
}

.left-section {
	width: 400px;
	position: relative;

	.overlay {
		position: absolute;
		align-items: center;
		display: flex;
		bottom: 0px;
		right: 0px;
		left: 0px;
		top: 0px;
	}

	.logo {
		margin-left: 30px;
		cursor: pointer;
	}
}

.tv-aula-title {
	@extend .type-h1;
	color: $branco;
	margin-left: 45px;
}

.center {
	flex-grow: 1;
	color: $branco;
	text-align: center;
	line-height: 112px;
	white-space: nowrap;
	@extend .type-hero;
}

.right-section {
	width: 400px;
	display: flex;
	flex-direction: row-reverse;
	padding-right: 30px;
	align-items: center;

	.pct {
		color: $branco;
		font-size: 40px;
	}

	.time {
		@extend .type-h2;
		line-height: 40px;
		padding-right: 15px;
		color: $branco;
	}
}

.tv-aula-body {
	position: relative;
	overflow-x: hidden;

	.scroll-content {
		padding: 30px;
		height: calc(100vh - 112px);
		width: calc(133% - 10px);
		display: grid;
		position: relative;
		right: 0px;
		grid-template-columns: 1fr 1fr 1fr 1fr;
		gap: 30px;
	}
}
