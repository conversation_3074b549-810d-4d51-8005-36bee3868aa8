<pacto-cat-layout-v2>
	<div class="titulo">Cartão de crédito</div>
	<div class="container-mini-card">
		<pacto-cat-grid-mini-card
			[headerText]="'Último envio de cobrança'"
			[icon]="'pct-repeat'"
			[type]="'AZULIM'"
			class="minicard">
			<div card-body>
				<label class="body-mini-card">{{ ultimaExecucao }}</label>
			</div>
		</pacto-cat-grid-mini-card>
		<pacto-cat-grid-mini-card
			(click)="openModalDetalhamentoDeCredito()"
			[headerText]="'Crédito Pacto'"
			[icon]="'pct-dollar-sign'"
			[ngClass]="{ minicard: true, clickavel: utilizados }"
			[type]="'CHUCHUZINHO'"
			id="credito-pacto">
			<div card-body>
				<label class="body-mini-card"></label>
				<div class="label-descricao">{{ descricaoCredito }}</div>
				<div *ngIf="utilizados" class="label-credito">{{ creditoPacto }}</div>
			</div>
		</pacto-cat-grid-mini-card>
	</div>

	<pacto-pay-filter
		#payFilter
		(obterFiltro)="filtrar($event)"
		[includesConvenio]="true"
		[includesEmpresa]="true"
		[includesPeriodo]="true"
		[preservarFiltro]="true"></pacto-pay-filter>

	<pacto-pay-filter-info
		(removerFiltro)="payFilter.definirValor($event)"
		[filtro]="filtroInfo"></pacto-pay-filter-info>

	<h2 class="parcelas">Parcelas</h2>
	<div class="flex-space details details-cartao">
		<pacto-cat-grid-square-card
			(click)="verParcelas(indicador)"
			*ngFor="let indicador of indicadores"
			[class.gradient]="indicador.nome === 'PENDENTES'">
			<div card-title>{{ indicador.nome | captalize }}</div>
			<div card-body>
				<div class="flex-center">
					<p>R$</p>
					<h2>{{ splitValor(indicador.valor).inteiro }}</h2>
					<p style="align-self: flex-end">
						,{{ splitValor(indicador.valor).decimal }}
					</p>
				</div>
				<div class="flex-center">
					<label>Qnt.:</label>
					<label>{{ indicador.qtd }}</label>
				</div>
			</div>
		</pacto-cat-grid-square-card>
	</div>
	<div class="content">
		<div
			class="table-wrapper pacto-shadow"
			style="
				background: #ffffff;
				box-shadow: 0px 2px 4px #e4e5e6;
				border-radius: 8px;
			">
			<pacto-relatorio-cobranca
				#relatorio
				[emptyStateMessage]="'Não existe dados no período selecionado'"
				[hidden]="tableDetalhar"
				[tableTitle]="
					'Histórico de transações e remessas'
				"></pacto-relatorio-cobranca>

			<pacto-detalhar-cobranca
				*ngIf="tableDetalhar"
				[data]="dataTransacao"
				[filtro]="filtro"
				[valorTransacao]="valorTransacao"></pacto-detalhar-cobranca>
		</div>
	</div>
</pacto-cat-layout-v2>

<ng-template #celulaCobranca let-transacao="item">
	<span>
		Qnt.: {{ transacao.qtdTotal }} -
		{{ transacao.valorTotal | currency : "BRL" }}
	</span>
</ng-template>
<ng-template #celulaAprovadas let-transacao="item">
	<span>
		Qnt.: {{ transacao.qtdRecebidos }} -
		{{ transacao.valorRecebidos | currency : "BRL" }}
	</span>
</ng-template>

<ng-template #celulaPendentes let-transacao="item">
	<span>
		Qnt.: {{ transacao.qtdPendentes }} -
		{{ transacao.valorPendentes | currency : "BRL" }}
	</span>
</ng-template>

<ng-template #celulaNegadas let-transacao="item">
	<span>
		Qnt.: {{ transacao.qtdNaoAprovada }} -
		{{ transacao.valorNaoAprovada | currency : "BRL" }}
	</span>
</ng-template>

<ng-template #celulaStatus let-transacao="item">
	<div *ngIf="transacao.status === 'PROCESSADO'" class="t-status t-status-paid">
		<span>PROCESSADA</span>
	</div>
	<div *ngIf="transacao.status === 'AGUARDANDO'" class="t-status t-status-wait">
		<span>AGUARDANDO</span>
	</div>
</ng-template>

<ng-template #celulaVerMais let-transacao="item">
	<i
		(click)="detalharTransacao(transacao)"
		class="pct pct-arrow-up-right"
		style="font-size: 20px; cursor: pointer"></i>
</ng-template>
