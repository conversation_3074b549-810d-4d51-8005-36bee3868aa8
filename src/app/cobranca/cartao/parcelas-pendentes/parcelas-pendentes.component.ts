import {
	animate,
	state,
	style,
	transition,
	trigger,
} from "@angular/animations";
import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import {
	DadosDoFiltro,
	Filtro,
	IndicadorDeParcelas,
	PactoPayApiCartaoService,
	PactoPayApiDashService,
} from "pactopay-api";
import { PactoModalSize } from "ui-kit";
import { ConvenioTipo, ZwPactoPayApiDashService } from "zw-pactopay-api";
import { DetalhamentoDeCreditoComponent } from "../../components/detalhamento-de-credito/detalhamento-de-credito.component";
import { ModalEnvioParcelaComponent } from "../../components/modal-envio-parcela/modal-envio-parcela.component";
import { ModalErroParcelaComponent } from "../../components/modal-erro-parcela/modal-erro-parcela.component";
import { ModalSelecionarConvenioComponent } from "../../components/modal-selecionar-convenio/modal-selecionar-convenio.component";

@Component({
	selector: "pacto-parcelas-pendentes",
	templateUrl: "./parcelas-pendentes.component.html",
	styleUrls: ["./parcelas-pendentes.component.scss", "../../cobranca.scss"],
	animations: [
		trigger("openClose", [
			state("true", style({ height: "*" })),
			state("false", style({ height: "0px" })),
			transition("false <=> true", animate("0.2s ease")),
		]),
	],
})
export class ParcelasPendentesComponent implements OnInit {
	public filtro: Filtro;
	public filtroInfo: DadosDoFiltro;

	public indicador: IndicadorDeParcelas;
	public ultimaExecucao: string;
	public creditoPacto: number;
	public parcelasToogle: Array<any>;
	public open: Array<boolean> = [];
	public selectedItems: Array<any> = [];
	public descricaoCredito: string;
	public utilizados: boolean;
	public podeEnviarCobranca = false;

	public allowsCheck = {
		pendenteRetorno: false,
		seraCobradaHojeAutomaticamente: false,
		foiCobradaHojeManualmente: false,
	};

	constructor(
		private readonly cd: ChangeDetectorRef,
		private readonly pactoPayApiCartao: PactoPayApiCartaoService,
		private readonly pactoPayApiDash: PactoPayApiDashService,
		private readonly modalService: ModalService,
		private readonly zwPactoPayApiDash: ZwPactoPayApiDashService,
		private readonly sessionService: SessionService
	) {}

	public ngOnInit() {}

	public filtrar(event) {
		this.filtro = event.filtro;
		this.filtroInfo = event.dados;

		this.obterParcelasPendentes();

		this.zwPactoPayApiDash.obterCredito().subscribe((result) => {
			this.utilizados = result.utilizados;
			this.creditoPacto = result.quantidade;
			this.descricaoCredito = result.utilizados
				? "Utilizados:"
				: result.descricao;
			this.cd.detectChanges();
		});

		this.pactoPayApiDash.obterUltimaExecucao().subscribe((result) => {
			this.ultimaExecucao = result;
			this.cd.detectChanges();
		});

		this.pactoPayApiCartao.obterIndicadores(this.filtro).subscribe(
			(res) => {
				if (res.content) {
					this.indicador = res.content.find((i) => i.indicador === 3);
				} else {
					console.log(res.meta);
				}
				this.cd.detectChanges();
			},
			(error) => console.log(error)
		);
	}

	private obterParcelasPendentes(): void {
		this.zwPactoPayApiDash
			.obterParcelasPendentes(this.filtro)
			.subscribe((res) => {
				this.parcelasToogle = res;
				this.cd.detectChanges();
			});
	}

	public transformMoneyValue(value: number) {
		return value
			? value.toLocaleString("pt-BR", {
					minimumFractionDigits: 2,
					style: "currency",
					currency: "BRL",
			  })
			: value;
	}

	public setSelectedItems(event: any) {
		if (event.clearAll) {
			for (const row of this.selectedItems.filter(
				(item) => item.id === event.id
			)) {
				const removeIndex = this.selectedItems.indexOf(row);
				this.selectedItems.splice(removeIndex, 1);
			}
		} else if (event.selectedAll) {
			for (const row of event.selectedItems) {
				if (!this.selectedItems.map((item) => item.row).includes(row)) {
					this.selectedItems.push({ id: event.id, row });
				}
			}
		} else {
			if (this.selectedItems.map((item) => item.row).includes(event.row)) {
				const removeIndex = this.selectedItems
					.map((item) => item.row)
					.indexOf(event.row);
				this.selectedItems.splice(removeIndex, 1);
			} else {
				this.selectedItems.push({ id: event.id, row: event.row });
			}
		}

		this.podeEnviarCobranca = this.selectedItems.length > 0;
		this.cd.detectChanges();
	}

	public headerClickHandler(i: number) {
		this.open[i] = !this.open[i];
	}

	public isOpen(index: number) {
		if (this.open && this.open[index]) {
			this.open[index] = true;
			return true;
		} else {
			this.open[index] = false;
			return false;
		}
	}

	public enviarParcelas() {
		this.zwPactoPayApiDash
			.getConvenioPorTipo(
				ConvenioTipo.CARTAO,
				Number(this.sessionService.empresaId)
			)
			.subscribe((res) => {
				const pctModal = this.modalService.open(
					"",
					ModalSelecionarConvenioComponent
				);
				const convenios = res.map((convenio) => {
					return { value: convenio.codigo, label: convenio.descricao };
				});
				pctModal.componentInstance.convenios = convenios;
				pctModal.componentInstance.convenioSelecionado.subscribe((convenio) => {
					const parcelas = Array.from(
						new Set(this.selectedItems.map((item) => item.row.codigo))
					);

					const body: Partial<any> = {};
					body.parcelas = parcelas;
					body.convenio = convenio;
					body.empresa = this.sessionService.empresaId;

					if (this.sessionService.loggedUser.usuarioZw > 0) {
						body.usuario = this.sessionService.loggedUser.usuarioZw;
					} else {
						body.username = this.sessionService.loggedUser.username;
					}

					this.zwPactoPayApiDash.enviarCobranca(body).subscribe((result) => {
						if (result.error) {
							const modal = this.modalService.open(
								"",
								ModalErroParcelaComponent
							);
							modal.componentInstance.resultado =
								result.error.error.meta.message;
						} else {
							const modal = this.modalService.open(
								"",
								ModalEnvioParcelaComponent
							);
							modal.result.then(() => this.obterParcelasPendentes());
							modal.result.then().catch(() => this.obterParcelasPendentes());
							this.selectedItems = [];
						}
					});
				});
			});
	}

	public openModalDetalhamentoDeCredito(): void {
		if (this.utilizados) {
			const modal = this.modalService.open(
				"Detalhamento de crédito Pacto",
				DetalhamentoDeCreditoComponent,
				PactoModalSize.LARGE
			);
			modal.componentInstance.empresas = this.filtro.empresas;
		}
	}

	public splitValor(valor: number): { inteiro: string; decimal: string } {
		const valorFormatado = valor
			.toLocaleString("pt-br", { minimumFractionDigits: 2 })
			.split(",");
		return {
			inteiro: valorFormatado[0],
			decimal: valorFormatado[1],
		};
	}
}
