<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'BENCHMARKS'
		}"
		class="first"></pacto-breadcrumbs>

	<div *ngIf="ready" class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="btnEditHandler($event)"
			[filterConfig]="filterConfig"
			[sessionService]="sessionServive"
			[tableDescription]="subtitulo"
			[tableTitle]="titulo"
			[table]="table"
			telaId="crossBenchmarks"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<!--title table-->
<ng-template #titulo>
	<span i18n="@@crud-atividade:title">Benchmarks</span>
</ng-template>
<ng-template #subtitulo>
	<span i18n="@@crud-ambientes:description">
		Gere<PERSON>ie os benchmarks cadastrados.
	</span>
</ng-template>
<!--End title table-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Adicionar</span>
</ng-template>
<!--table columns-->
<ng-template #nomeColumnName>
	<span i18n="@@crud-benchmarks:table:nome">Nome</span>
</ng-template>
<ng-template #tipoExercicioColumnName>
	<span i18n="@@crud-benchmarks:table:tipo-exercicio">Tipo de Exercício</span>
</ng-template>
<ng-template #exerciciosColumnName>
	<span i18n="@@crud-benchmarks:table:tipo-exercicio">Exercícios</span>
</ng-template>
<ng-template #acaoColumnName>
	<span i18n="@@crud-benchmarks:table:acoes">Ações</span>
</ng-template>
<!--labels filters-->
<ng-template #tipoExercicios>
	<span i18n="@@crud-benchmarks:select:tipo-exercicio:label">
		Tipo de Exercício
	</span>
</ng-template>
<ng-template #tipoBenchmarkColumnName>
	<span i18n="@@crud-benchmarks:select:tipos-benchmark:label">
		Tipo de Benchmark
	</span>
</ng-template>

<!--Celulas para formatação-->
<ng-template #tipoBenchmarkCelula let-item="item">
	{{ item.tipoBenchmark?.nome }}
</ng-template>
<!--fim-->
<!--tipo exercicio translator filter-->
<ng-template #tipoExercicioTranslator let-name="name">
	<ng-container [ngSwitch]="name">
		<span *ngSwitchCase="'FOR_TIME'" i18n="@@crossfit:benchmarks:options:time">
			For Time
		</span>
		<span
			*ngSwitchCase="'FOR_REPS'"
			i18n="@@crossfit:benchmarks:options:reps-time">
			For Reps and Time
		</span>
		<span
			*ngSwitchCase="'FOR_WEIGHT'"
			i18n="@@crossfit:benchmarks:options:weight">
			For Weight
		</span>
		<span *ngSwitchCase="'AMRAP'" i18n="@@crossfit:benchmarks:options:amrap">
			Amrap
		</span>
	</ng-container>
</ng-template>

<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-ambientes:remover:tooltip-icon">
	Remover
</span>
<span #tooltipEditar [hidden]="true">Editar</span>
<span
	#removeModalTitle
	[hidden]="true"
	i18n="@@crud-benchmarks:remove:modal:title">
	Remover Benchmark ?
</span>
<span
	#removeModalBody
	[hidden]="true"
	i18n="@@crud-benchmarks:remove:modal:body">
	Deseja remover o benchmark {{ nomeBenchmark }}?
</span>
<span #removeSuccess [hidden]="true" i18n="@@crud-benchmarks:remove:success">
	Benchmark removido com sucesso.
</span>

<!--SITUACAO TRANSLATOR-->
<ng-template #tipoTranslatorColumn let-item="item">
	<span
		*ngIf="item.tipoExercicio === 'FOR_TIME'"
		i18n="@@crossfit:benchmarks:options:time">
		For Time
	</span>
	<span
		*ngIf="item.tipoExercicio === 'FOR_REPS'"
		i18n="@@crossfit:benchmarks:options:reps-time">
		For Reps and Time
	</span>
	<span
		*ngIf="item.tipoExercicio === 'FOR_WEIGHT'"
		i18n="@@crossfit:benchmarks:options:weight">
		For Weight
	</span>
	<span
		*ngIf="item.tipoExercicio === 'AMRAP'"
		i18n="@@crossfit:benchmarks:options:amrap">
		Amrap
	</span>
</ng-template>
