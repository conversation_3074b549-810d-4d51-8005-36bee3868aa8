import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { CrossfitCoreModule } from "@crossfit-core/crossfit-core.module";
import { CrossfitSharedModule } from "./crossfit-shared/crossfit-shared.module";
import { HomeComponent } from "./home/<USER>";
import { HomePageModule } from "pacto-layout";

const crossfit = true;
const routes: Routes = [
	{
		path: "cadastros/aparelhos",
		loadChildren: () =>
			import("src/app/base/aparelho/aparelho.routing.module").then(
				(m) => m.AparelhoRoutingModule
			),
		data: { crossfit },
	},
	{
		path: "cadastros/atividades-cross",
		loadChildren: () =>
			import("./atividade-crossfit/atividade-crossfit.module").then(
				(m) => m.AtividadeCrossfitModule
			),
	},
	{
		path: "cadastros/benchmarks",
		loadChildren: () =>
			import("./benchmark/benchmark.module").then((m) => m.<PERSON>chmarkModule),
	},
	{
		path: "cadastros/tipos-benchmark",
		loadChildren: () =>
			import("./tipo-benchmark/tipo-benchmark.module").then(
				(m) => m.TipoBenchmarkModule
			),
	},
	{
		path: "cadastros/wods",
		loadChildren: () => import("./wod/wod.module").then((m) => m.WodModule),
	},
	{
		path: "cadastros/tipos-wod",
		loadChildren: () =>
			import("./tipo-wod/tipo-wod.module").then((m) => m.TipoWodModule),
	},
	{
		path: "bi",
		loadChildren: () =>
			import("./crossfit-bi/crossfit-bi.module").then(
				(m) => m.CrossfitBiModule
			),
	},
	{
		path: "",
		redirectTo: "home",
	},
	{
		path: "home",
		component: HomeComponent,
	},
	{
		path: "monitor",
		loadChildren: () =>
			import("./monitor/monitor.module").then((m) => m.MonitorModule),
	},
	{
		path: "cadastros/nivel-wod",
		loadChildren: () =>
			import("./nivel-wod/nivel-wod.module").then((m) => m.NivelWodModule),
	},
];

@NgModule({
	imports: [
		CommonModule,
		BaseSharedModule,
		CrossfitCoreModule,
		CrossfitSharedModule,
		RouterModule.forChild(routes),
		HomePageModule,
	],
	declarations: [HomeComponent],
})
export class CrossfitModule {}
