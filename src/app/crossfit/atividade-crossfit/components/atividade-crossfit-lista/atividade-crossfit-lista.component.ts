import { Component, OnInit, ViewChild } from "@angular/core";
import { SnotifyService } from "ng-snotify";
import { ModalService } from "@base-core/modal/modal.service";
import { TreinoApiAtividadeCrossfitService } from "treino-api";
import { PactoDataGridConfig } from "ui-kit";
import { Router } from "@angular/router";
import { RestService } from "@base-core/rest/rest.service";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-atividade-crossfit-lista",
	templateUrl: "./atividade-crossfit-lista.component.html",
	styleUrls: ["./atividade-crossfit-lista.component.scss"],
})
export class AtividadeCrossfitListaComponent implements OnInit {
	@ViewChild("addLabel", { static: true }) addLabel;
	@ViewChild("colunaNome", { static: true }) colunaNome;
	@ViewChild("colunaSituacao", { static: true }) colunaSituacao;
	@ViewChild("statusColumn", { static: true }) statusColumn;
	@ViewChild("tableData", { static: true }) tableData;
	@ViewChild("tooltipAtivar", { static: true }) tooltipAtivar;
	@ViewChild("tooltipInativar", { static: true }) tooltipInativar;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;

	@ViewChild("ativarModalTitle", { static: true }) ativarModalTitle;
	@ViewChild("ativarModalBody", { static: true }) ativarModalBody;
	@ViewChild("ativarSuccess", { static: true }) ativarSuccess;
	@ViewChild("inativarModalTitle", { static: true }) inativarModalTitle;
	@ViewChild("inativarModalBody", { static: true }) inativarModalBody;
	@ViewChild("inativarSuccess", { static: true }) inativarSuccess;
	@ViewChild("inativarBtn", { static: true }) inativarBtn;
	@ViewChild("ativarBtn", { static: true }) ativarBtn;

	constructor(
		private atividadeService: TreinoApiAtividadeCrossfitService,
		private snotifyService: SnotifyService,
		private modalService: ModalService,
		private rest: RestService,
		public sessionServive: SessionService,
		private router: Router
	) {}

	table: PactoDataGridConfig;
	nomeAtividade = "";

	ngOnInit() {
		this.configTable();
	}

	private configTable() {
		const tooltipInativar = this.tooltipInativar.nativeElement.innerHTML;
		const tooltipAtivar = this.tooltipAtivar.nativeElement.innerHTML;
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;

		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("atividades-crossfit"),
			logUrl: this.rest.buildFullUrl("log/atividades-crossfit"),
			quickSearch: true,
			buttons: {
				conteudo: this.addLabel,
				nome: "add",
				id: "btn-novo-atividade-crossfit",
			},
			columns: [
				{
					nome: "nome",
					titulo: this.colunaNome,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "ativo",
					titulo: this.colunaSituacao,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "ativo",
					celula: this.statusColumn,
				},
			],
			actions: [
				{
					nome: "remove",
					iconClass: "fa fa-minus-square-o",
					tooltipText: tooltipInativar,
					showIconFn: (row) => row.ativo,
				},
				{
					nome: "remove",
					iconClass: "fa fa-check-square-o",
					tooltipText: tooltipAtivar,
					showIconFn: (row) => !row.ativo,
				},
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
					tooltipText: tooltipEditar,
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "remove") {
			this.remove($event.row);
		} else if ($event.iconName === "edit") {
			this.router.navigate([
				"cross",
				"cadastros",
				"atividades-cross",
				$event.row.id,
			]);
		}
	}

	rowClickHandler(row: any) {
		this.router.navigate(["cross", "cadastros", "atividades-cross", row.id]);
	}

	btnClickHandler($event) {
		this.router.navigate([
			"cross",
			"cadastros",
			"atividades-cross",
			"adicionar",
		]);
	}

	private remove(item) {
		this.nomeAtividade = item.nome;
		setTimeout(() => {
			let modalTitle = "";
			let modalBody = "";
			let mensagemSuccess = "";
			let btn = "";
			if (item.ativo) {
				modalTitle = this.inativarModalTitle.nativeElement.innerHTML;
				modalBody = this.inativarModalBody.nativeElement.innerHTML;
				mensagemSuccess = this.inativarSuccess.nativeElement.innerHTML;
				btn = this.inativarBtn.nativeElement.innerHTML;
			} else {
				modalTitle = this.ativarModalTitle.nativeElement.innerHTML;
				modalBody = this.ativarModalBody.nativeElement.innerHTML;
				mensagemSuccess = this.ativarSuccess.nativeElement.innerHTML;
				btn = this.ativarBtn.nativeElement.innerHTML;
			}
			const handler = this.modalService.confirm(modalTitle, modalBody, btn);
			handler.result.then(() => {
				this.atividadeService.atualizarSituacao(item.id).subscribe(() => {
					this.tableData.reloadData();
					this.snotifyService.success(mensagemSuccess);
				});
			});
		});
	}
}
