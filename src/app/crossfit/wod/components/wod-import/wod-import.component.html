<div>
	<div class="modal-header">
		<h4 class="modal-title">
			<span i18n="@@crossfit:crud-wods:importar:title">Importar dados</span>
		</h4>
		<button
			(click)="dismiss()"
			aria-label="Close"
			class="close modal-item"
			type="button">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>
	<div class="modal-body">
		<div class="row">
			<div class="col-md-12">
				<div class="form-group">
					<label
						class="control-label"
						i18n="@@crossfit:crud-wods:importar:origem-wod:label">
						Origem WOD
					</label>
					<select
						(change)="changeCarregarBenchmark()"
						[formControl]="formGroup.get('origemWod')"
						class="form-control form-control-sm">
						<option
							i18n="@@crossfit:crud-wods:importar:benchmark:option"
							value="1">
							Benchmark
						</option>
						<option
							i18n="@@crossfit:crud-wods:importar:url-crossfit:option"
							value="2">
							https://www.cross.com/
						</option>
					</select>
				</div>
			</div>
		</div>
		<div class="row">
			<div *ngIf="formGroup.get('origemWod').value == '1'" class="col-md-12">
				<pacto-cat-select-filter
					[control]="formGroup.get('benchmark')"
					[id]="'pacto-select-professor'"
					[labelKey]="'nome'"
					[label]="'Benchmark'"
					[options]="benchmarks"
					[placeholder]="'Selecione um Benchmark.'"
					[size]="'SMALL'"></pacto-cat-select-filter>
			</div>
			<div *ngIf="formGroup.get('origemWod').value == '2'" class="col-md-6">
				<pacto-datepicker
					[control]="formGroup.get('diaCrossfit')"
					[maxDate]="dataAtual"
					[name]="'dia'"
					i18n-label="@@crossfit:crud-wods:datepicker:dia:label"
					i18n-message="@@crossfit:crud-wods:datepicker:dia:message"
					label="Dia"
					message="Dia do wod"></pacto-datepicker>
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button
			(click)="dismiss()"
			class="btn btn-secondary modal-item"
			i18n="@@buttons:cancelar"
			type="button">
			Cancelar
		</button>
		<button
			(click)="close()"
			class="btn btn-primary modal-item"
			i18n="@@crossfit:crud-wods:importar:button:importar-dados"
			type="button">
			Importar dados
		</button>
	</div>
</div>

<ng-template #benchmarkTemplate let-item="item">
	<div class="user-item-wrapper">
		<div class="name">
			{{ item.nome }}
		</div>
	</div>
</ng-template>
