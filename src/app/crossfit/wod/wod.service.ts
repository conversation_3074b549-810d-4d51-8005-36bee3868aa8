import { Injectable } from "@angular/core";
import { CrossfitCoreModule } from "@crossfit-core/crossfit-core.module";
import { HttpClient } from "@angular/common/http";
import { RestService } from "@base-core/rest/rest.service";

import { Observable } from "rxjs";
import { map } from "rxjs/operators";

@Injectable({
	providedIn: CrossfitCoreModule,
})
export class WodService {
	constructor(private http: HttpClient, private restService: RestService) {}

	podeCompartilhar(chave): Observable<boolean> {
		const url = this.restService.buildFullUrlOamd(
			`prest/empresaFinanceiro/compartilharWod?`
		);
		return this.http.get(url, { params: { chaveZW: chave } }).pipe(
			map((result: any) => {
				return result.compartilharWod;
			})
		);
	}
}
