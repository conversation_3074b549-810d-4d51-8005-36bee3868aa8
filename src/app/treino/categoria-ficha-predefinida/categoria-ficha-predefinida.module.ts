import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import {
	PerfilAcessoRecursoNome,
	PerfilAcessoRecurso,
	PerfilRecursoPermissoTipo,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";
import { MontagemTreinoModule } from "../montagem-treino/montagem-treino.module";
import { CategoriaFichaPredefinidaListaComponent } from "./categoria-ficha-predefinida-lista/categoria-ficha-predefinida-lista.component";
import { CategoriaFichaPredefinidaEditComponent } from "./categoria-ficha-predefinida-edit/categoria-ficha-predefinida-edit.component";

const recurso = new PerfilAcessoRecurso(
	PerfilAcessoRecursoNome.FICHAS_PRE_DEFINIDAS,
	[
		PerfilRecursoPermissoTipo.CONSULTAR,
		PerfilRecursoPermissoTipo.TOTAL,
		PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
		PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
	]
);

const routes: Routes = [
	{
		path: "list",
		component: CategoriaFichaPredefinidaListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
];

@NgModule({
	imports: [
		RouterModule.forChild(routes),
		BaseSharedModule,
		CommonModule,
		MontagemTreinoModule,
	],
	entryComponents: [CategoriaFichaPredefinidaEditComponent],
	declarations: [
		CategoriaFichaPredefinidaListaComponent,
		CategoriaFichaPredefinidaEditComponent,
	],
})
export class CategoriaFichaPredefinidaModule {}
