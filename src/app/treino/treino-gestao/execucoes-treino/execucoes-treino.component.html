<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: traducoes.getLabel('titleTopo'),
			menu: traducoes.getLabel('titleTopo2')
		}"></pacto-breadcrumbs>
	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			*ngIf="ready"
			[filterConfig]="filterConfig"
			[sessionService]="sessionService"
			[tableDescription]="subTitulo"
			[tableTitle]="titulo"
			[table]="table"
			telaId="execucoesdetreino"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<ng-template #titulo>
	<span i18n="@@execucoes-treino:title">Execuções de Treino</span>
</ng-template>

<ng-template #subTitulo>
	<span i18n="@@execucoes-treino:subTitle">Gestão de Execuções de Treino</span>
</ng-template>
<ng-template #columnMatricula>
	<span i18n="@@execucoes-treino:matricula-aluno">Matrícula</span>
</ng-template>
<ng-template #columnAluno>
	<span i18n="@@execucoes-treino:aluno">Aluno</span>
</ng-template>

<ng-template #columnProfessor>
	<span i18n="@@execucoes-treino:professor">Professor</span>
</ng-template>

<ng-template #columnExecucoes>
	<span i18n="@@execucoes-treino:execucoes">Execuções</span>
</ng-template>
<ng-template #intervaloPesquisaLabel>
	<span>Intervalo da Pesquisa</span>
</ng-template>

<ng-template #dataInicioLabel i18n="@@execucoes-treino:data-inicio:filtro">
	Data Início
</ng-template>
<ng-template #dataFimLabel i18n="@@execucoes-treino:data-fim:filtro">
	Data Fim
</ng-template>
<ng-template #professorLabel i18n="@@execucoes-treino:professores:filtro">
	Professores
</ng-template>
<ng-template
	#origemExecucaoLabel
	i18n="@@execucoes-treino:origem-execucao:filtro">
	Origem Execução
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@execucoes-treino:titleTopo" xingling="titleTopo">
		Relatórios
	</span>
	<span i18n="@@execucoes-treino:titleTopo2" xingling="titleTopo2">
		Execuções de Treino
	</span>
</pacto-traducoes-xingling>
