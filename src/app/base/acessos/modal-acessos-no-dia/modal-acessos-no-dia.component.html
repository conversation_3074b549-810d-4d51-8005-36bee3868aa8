<section>
	<div class="row subtitle">
		<div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
			<span class="text">
				O aluno entrou e saiu da academia hoje nos seguintes horários:
			</span>
		</div>
	</div>
	<div class="row">
		<div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
			<table class="table">
				<thead>
					<tr>
						<th scope="col">
							<span class="entradas">Entrada</span>
						</th>
						<th scope="col">Saída</th>
						<th scope="col">Ações</th>
					</tr>
				</thead>
				<tbody>
					<tr *ngFor="let acesso of listaAcessosDia">
						<td class="data">
							<span>
								{{ acesso.dtHrEntrada | date : "dd/MM/yyyy HH:mm" }}
							</span>
						</td>
						<td class="data">
							{{ acesso.dtHrSaida | date : "dd/MM/yyyy HH:mm" }}
						</td>
						<td>
							<i (click)="edit(acesso)" class="pct pct-edit"></i>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>

	<div class="row">
		<div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 btn">
			<pacto-cat-button
				(click)="registrarAcesso()"
				label="Registrar"
				size="LARGE"
				style="width: 108px; height: 40px"
				type="PRIMARY"></pacto-cat-button>
		</div>
	</div>
</section>
