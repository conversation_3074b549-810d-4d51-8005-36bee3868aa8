<div class="objetivos-title" i18n="@@catalogo-alunos:objetivos:title">
	Objetivos
</div>
<div *ngIf="controls" class="input-group-wrapper objetivos-wrapper">
	<div *ngFor="let objetivo of objetivos; let index = index" class="form-check">
		<ng-container *ngIf="controls.at(index)">
			<input
				[formControl]="controls.at(index)"
				class="form-check-input"
				id="{{ objetivo.id }}"
				type="checkbox" />
			<label class="form-check-label" for="{{ objetivo.id }}">
				{{ objetivo.nome }}
			</label>
		</ng-container>
	</div>
</div>
