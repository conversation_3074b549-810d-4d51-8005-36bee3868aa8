import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnChanges,
	OnInit,
	Output,
} from "@angular/core";
import {
	PerfilAcessoRecursoNome,
	TreinoApiAvaliacaoFisicaService,
} from "treino-api";
import { Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { FormControl, FormGroup } from "@angular/forms";
import { AvaliacaoIntegrada } from "../../../../../projects/treino-api/src/lib/avaliacao.model";
import { map } from "rxjs/operators";
import { Observable } from "rxjs";

@Component({
	selector: "pacto-cat-lista-avaliacao-integrada",
	templateUrl: "./cat-lista-avaliacao-integrada.component.html",
	styleUrls: ["./cat-lista-avaliacao-integrada.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatListaAvaliacaoIntegradaComponent implements OnInit, OnChanges {
	@Input() alunoId: number;
	@Input() permissaoExcluir: boolean;
	@Input() avaliacoes: AvaliacaoIntegrada[] = [];
	@Output() remove: EventEmitter<AvaliacaoIntegrada> = new EventEmitter();

	listAvSelecionadas: number[] = [];

	fc = new FormGroup({
		ckAvIntegrada: new FormControl(false),
	});

	constructor(
		private cd: ChangeDetectorRef,
		private router: Router,
		private snotify: SnotifyService,
		private sessionService: SessionService,
		private avaliacaoFisicaService: TreinoApiAvaliacaoFisicaService
	) {}

	ngOnInit() {}

	ngOnChanges() {
		const permissao = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.LANCAR_AVALICAO_RETROATIVA
		);
		this.permissaoExcluir = permissao && permissao.excluir;
		this.cd.detectChanges();
	}

	clickAvaliacaoHandler($event: MouseEvent, avaliacao: AvaliacaoIntegrada) {
		const permissao = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.LANCAR_AVALICAO_RETROATIVA
		);
		if ((permissao && permissao.editar) || permissao.consultar) {
			$event.preventDefault();
			$event.stopPropagation();
			const avaliacaoId = avaliacao.id;
			const alunoId = this.alunoId;
			this.router.navigate([
				"/avaliacao",
				"avaliacao-integrada-aluno",
				alunoId,
				"avaliacao",
				avaliacaoId,
			]);
		} else {
			this.snotify.warning(
				"Seu usuário não possui permissão, procure seu administrador"
			);
		}
	}

	removeHandler($event: MouseEvent, avaliacao: AvaliacaoIntegrada) {
		$event.preventDefault();
		$event.stopPropagation();
		const permissao = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.LANCAR_AVALICAO_RETROATIVA
		);
		if (permissao && permissao.excluir) {
			this.remove.emit(avaliacao);
		} else {
			this.snotify.warning(
				"Seu usuário não possui permissão, procure seu administrador"
			);
		}
	}

	printHandler($event: MouseEvent, avaliacao: AvaliacaoIntegrada) {
		$event.preventDefault();
		$event.stopPropagation();
		this.obterLinkPdfAvIntegrada(avaliacao.id).subscribe((linkAvIntegrada) => {
			window.open(linkAvIntegrada, "_blank");
		});
	}

	private obterLinkPdfAvIntegrada(avaliacaoId): Observable<any> {
		return this.avaliacaoFisicaService
			.obterLinkPdfAvIntegrada(avaliacaoId)
			.pipe(
				map((linkAvIntegrada) => {
					return linkAvIntegrada;
				})
			);
	}

	imprimirGrupoAvaliacoesIntegradas() {
		if (this.listAvSelecionadas.length < 2) {
			this.snotify.warning("Selecione duas ou mais avaliações para comparar.");
		} else if (this.listAvSelecionadas.length > 5) {
			this.snotify.warning("Selecione no máximo 5 avaliações");
		} else {
			let avaliacoesId = "";
			this.listAvSelecionadas.forEach((item) => {
				avaliacoesId += item + "_";
			});
			avaliacoesId = avaliacoesId.slice(0, -1);
			this.obterLinkPdfGrupoAvIntegrada(avaliacoesId).subscribe(
				(linkPdfGrupoAvIntegrada) => {
					window.open(linkPdfGrupoAvIntegrada, "_blank");
				}
			);
		}
	}

	private obterLinkPdfGrupoAvIntegrada(avaliacoesId): Observable<any> {
		return this.avaliacaoFisicaService
			.obterLinkPdfGrupoAvIntegrada(avaliacoesId)
			.pipe(
				map((linkPdfGrupoAvIntegrada) => {
					return linkPdfGrupoAvIntegrada;
				})
			);
	}

	checkAv(avaliacao: AvaliacaoIntegrada) {
		let removido = false;
		if (this.listAvSelecionadas.length === 0) {
			this.listAvSelecionadas.push(avaliacao.id);
		} else {
			this.listAvSelecionadas.forEach((item, index) => {
				if (item === avaliacao.id) {
					this.listAvSelecionadas.splice(index, 1);
					removido = true;
				}
			});

			if (!removido) {
				this.listAvSelecionadas.push(avaliacao.id);
			}
		}
	}

	sendHandler($event: MouseEvent, avaliacao: AvaliacaoIntegrada) {
		$event.preventDefault();
		$event.stopPropagation();
		this.enviarEmailLinkPdfIndividualAvIntegrada(avaliacao.id).subscribe(
			(response) => {
				if (response === true) {
					this.snotify.success("Avaliação de progresso enviada com sucesso!");
				} else {
					this.snotify.error(response);
				}
			}
		);
	}

	private enviarEmailLinkPdfIndividualAvIntegrada(
		avaliacaoId
	): Observable<any> {
		return this.avaliacaoFisicaService
			.enviarEmailLinkPdfIndividualAvIntegrada(this.alunoId, avaliacaoId)
			.pipe(
				map((response) => {
					return response;
				})
			);
	}

	sendGrupoAvaliacoesIntegradas() {
		if (this.listAvSelecionadas.length < 2) {
			this.snotify.warning("Selecione duas ou mais avaliações para comparar.");
		} else if (this.listAvSelecionadas.length > 5) {
			this.snotify.warning("Selecione no máximo 5 avaliações");
		} else {
			let avaliacoesId = "";
			this.listAvSelecionadas.forEach((item) => {
				avaliacoesId += item + "_";
			});
			avaliacoesId = avaliacoesId.slice(0, -1);
			this.enviarEmailLinkPdfGrupoAvIntegrada(avaliacoesId).subscribe(
				(response) => {
					if (response === true) {
						this.snotify.success("Avaliação de progresso enviada com sucesso!");
					} else {
						this.snotify.error(response);
					}
				}
			);
		}
	}

	private enviarEmailLinkPdfGrupoAvIntegrada(
		avaliacoesId: string
	): Observable<any> {
		return this.avaliacaoFisicaService
			.enviarEmailLinkPdfGrupoAvIntegrada(this.alunoId, avaliacoesId)
			.pipe(
				map((response) => {
					return response;
				})
			);
	}
}
