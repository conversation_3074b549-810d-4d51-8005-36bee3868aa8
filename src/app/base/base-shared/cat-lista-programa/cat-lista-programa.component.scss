@import "src/assets/scss/pacto/plataforma-import.scss";

.programa {
	display: flex;
	align-items: center;
	height: 60px;
	padding: 0px 10px;
	overflow: hidden;
	cursor: pointer;
	border-left: 4px solid $branco;

	&:nth-child(2n + 1) {
		border-left: 4px solid $cinzaClaroPri;
		background-color: $cinzaClaroPri;
	}

	&:hover {
		border-left: 4px solid $azulimPri;
	}

	.programa-nome {
		line-height: 1.2em;
		height: 20px;
		overflow: hidden;
		padding-right: 5px;
	}

	.programa-nome-wrapper {
		flex-grow: 1;
	}

	.range {
		line-height: 1em;
		padding-right: 5px;
		height: 12px;
		overflow: hidden;
		white-space: nowrap;
	}

	.actions {
		padding-right: 10px;

		.pct {
			color: $hellboy01;
			cursor: pointer;
		}
	}
}
