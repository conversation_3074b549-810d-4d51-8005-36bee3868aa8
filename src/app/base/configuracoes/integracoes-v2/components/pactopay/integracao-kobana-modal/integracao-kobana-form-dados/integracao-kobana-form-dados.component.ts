import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { TraducoesXinglingComponent } from "ui-kit";
import { Empresa } from "ms-pactopay-api";
import { FormControl, FormGroup } from "@angular/forms";

@Component({
	selector: "pacto-integracao-kobana-form-dados",
	templateUrl: "./integracao-kobana-form-dados.component.html",
	styleUrls: ["./integracao-kobana-form-dados.component.scss"],
})
export class IntegracaoKobanaFormDadosComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() empresa: Empresa;
	@Input() dadosIntegracaoKobana: any;
	formDados: FormGroup = new FormGroup({
		ambiente_apresentar: new FormControl(),
		account_number: new FormControl(),
		account_digit: new FormControl(),
		agency_number: new FormControl(),
		agency_digit: new FormControl(),
		api_access_token: new FormControl(),
	});

	constructor() {}

	ngOnInit() {
		this.initForm();
	}

	private initForm() {
		this.formDados.patchValue({
			ambiente_apresentar: this.dadosIntegracaoKobana.ambiente_apresentar,
			account_number: this.dadosIntegracaoKobana.account_number,
			account_digit: this.dadosIntegracaoKobana.account_digit,
			agency_number: this.dadosIntegracaoKobana.agency_number,
			agency_digit: this.dadosIntegracaoKobana.agency_digit,
			api_access_token: this.dadosIntegracaoKobana.api_access_token,
		});
		this.formDados.get("ambiente_apresentar").disable();
		this.formDados.get("account_number").disable();
		this.formDados.get("account_digit").disable();
		this.formDados.get("agency_number").disable();
		this.formDados.get("agency_digit").disable();
		this.formDados.get("api_access_token").disable();
	}
}
