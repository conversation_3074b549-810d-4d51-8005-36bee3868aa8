<div class="main-content">
	<pacto-cat-tabs-transparent
		id="abas-rd-station"
		#tabsMenus
		[tabIndex]="0"
		(activateTab)="tabClickHandler($event)"
		[actionLabel]="'Alterar empresa'"
		[actionIcon]="'pct pct-refresh-ccw'">
		<ng-template pactoTabTransparent="crm" label="CRM"></ng-template>
		<ng-template
			pactoTabTransparent="marketing"
			label="Marketing"></ng-template>
	</pacto-cat-tabs-transparent>
	<div class="aba-container" *ngIf="selectedTab === 'crm'">
		<div class="row">
			<div class="col-md-5 mr-auto">
				<pacto-cat-form-select-filter
					id="responsavel-padrao"
					i18n-label="@@integracao-rdstation:label-responsave-padrao"
					label="Responsável padrão"
					labelKey="nome"
					idKey="codigo"
					[control]="formGroup.get('responsavelPadrao')"
					[paramBuilder]="selectBuilder"
					[addEmptyOption]="true"
					[endpointUrl]="restService.buildFullUrlAdmCore('usuarios/meta')"
					[addtionalFilters]="
						getAditionalFiltersUsuariosMeta()
					"></pacto-cat-form-select-filter>
			</div>
			<div class="col-md-5">
				<pacto-cat-form-input
					id="hora-limite"
					i18n-label="@@integracao-rdstation:label-hora-limite"
					label="Hora limite atualização da meta"
					i18n-label="@@integracao-rdstation:placeholder-hora-limite"
					placeholder="00:00"
					[maxlength]="5"
					[textMask]="{ mask: timeMask(), guide: false }"
					[control]="formGroup.get('horaLimite')"></pacto-cat-form-input>
			</div>
		</div>
		<div class="row">
			<div class="col-md-5 mr-auto">
				<pacto-cat-form-select
					id="acao-objecao"
					i18n-label="@@integracao-rdstation:label-atualizar-lead-objecao"
					label="Tipo objeção para atualizar o Lead	"
					[control]="formGroup.get('acaoObjecao')"
					idKey="codigo"
					labelKey="descricao"
					[items]="acoesObjecoesLead"></pacto-cat-form-select>
			</div>
		</div>
		<div class="row">
			<div class="col-md-5 mr-auto">
				<div class="api-link">
					<div>
						<span>API v1.3</span>
					</div>
					<div class="btn-api">
						<pacto-cat-button
							id="btn-link-apiv13"
							class="btn-link"
							label="Link"
							size="NORMAL"
							width="83px"
							type="OUTLINE_GRAY"
							icon="copy cor-cinzaPri"
							iconPosition="before"
							title="Ao clicar no botão, o link será copiado para sua área de transferência e após isso basta colá-lo onde desejar no seu fluxo RD."
							(click)="copyLinkApi13()"></pacto-cat-button>
					</div>
				</div>
			</div>
			<div class="col-md-5 col-auto">
				<pacto-cat-form-select
					id="gatilho-v20"
					i18n-label="@@integracao-rdstation:label-gatilho"
					label="Gatilho v2.0"
					title="Escolha qual gatilho será considerado para o envio dos Leads, entre Oportunidade e Conversão. Se você escolher Oportunidade todos os Leads qualificados como oportunidade no RD Station serão enviados para a meta. Caso você opte por Conversão todos os leads que foram convertidos nas landing pages serão enviados para a meta diária, sem segmentação."
					[control]="formGroup.get('eventWeebHook')"
					idKey="id"
					labelKey="descricao"
					[items]="gatilhos"></pacto-cat-form-select>
			</div>
		</div>
		<div class="row row-check-box-cpf">
			<div class="col-md-5 mr-auto">
				<div class="api-link">
					<div>
						<span>API v2.0</span>
					</div>
					<div class="btn-api">
						<pacto-cat-button
							id="btn-aprovar"
							label="Aprovar"
							size="NORMAL"
							width="83px"
							type="OUTLINE_GRAY"
							title="Aprovar integração RD Station"
							(click)="aprovarIntegracaoRDStation()"></pacto-cat-button>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="aba-container" *ngIf="selectedTab === 'marketing'">
		<pacto-cat-checkbox
			id="checkbox-rdstation-markt"
			[label]="'Habilitar integração com a API RD Station Marketing'"
			[control]="
				toFormControl(formGroup.get('configAtualizarAlunoRdStationMarketing'))
			"></pacto-cat-checkbox>
		<p style="font-size: 15px; max-width: 600px">
			Com essa configuração habilitada, sempre que a situação de um aluno for
			alterada no sistema Pacto, as informações atualizadas desse aluno serão
			automaticamente enviadas ao RDStation.
		</p>
		<hr />
		<div style="display: flex; gap: 1rem; width: 100%">
			<pacto-cat-button
				id="btn-autenticar-api-rd-station-marketing"
				size="LARGE"
				type="PRIMARY"
				width="200px"
				[label]="'Autenticar integração'"
				[disabled]="
					!formGroup.get('configAtualizarAlunoRdStationMarketing')?.value ||
					this.integracao.configuracao.accessTokenRdStationMarketing
				"
				(click)="autenticarApiMarketing()"></pacto-cat-button>
		</div>
		<div
			*ngIf="
				formGroup.get('configAtualizarAlunoRdStationMarketing')?.value &&
				!this.integracao.configuracao.accessTokenRdStationMarketing
			"
			class="alert-container danger">
			<span class="alert-title">Atenção</span>
			<p>
				É necessário realizar a autenticação com a RD Station para concluir a
				integração com a API Marketing. Clique no botão acima.
			</p>
		</div>
		<div
			*ngIf="
				formGroup.get('configAtualizarAlunoRdStationMarketing')?.value &&
				this.integracao.configuracao.accessTokenRdStationMarketing
			"
			class="alert-container success">
			<div style="display: flex; justify-content: space-between; width: 100%">
				<span class="alert-title">
					Tudo certo! A integração já está autenticada. ✅
				</span>
				<pacto-cat-button
					id="switch-exibir-token"
					size="SMALL"
					type="SECONDARY"
					(click)="showToken()"
					[label]="
						this.exibirTokenAcesso ? 'Esconder' : 'Exibir'
					"></pacto-cat-button>
			</div>

			<p
				*ngIf="!this.exibirTokenAcesso"
				style="
					max-width: 300px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				">
				Token de acesso:
				{{ this.integracao.configuracao.accessTokenRdStationMarketing }}
			</p>
			<p *ngIf="this.exibirTokenAcesso" style="word-break: break-all">
				Token de acesso:
				{{ this.integracao.configuracao.accessTokenRdStationMarketing }}
			</p>
		</div>
	</div>
	<hr />
	<div class="row justify-content-end">
		<pacto-cat-button
			id="btn-ativar"
			size="LARGE"
			width="90px"
			type="PRIMARY"
			i18n-label="@@integracoes:btn-ativar-inativar"
			[label]="integracao.ativa ? 'Inativar' : 'Ativar'"
			style="margin-right: 10px"
			(click)="
				salvarIntegracao(integracao.ativa ? 'inativar' : 'ativar')
			"></pacto-cat-button>

		<pacto-cat-button
			id="btn-salvar"
			width="152px"
			size="LARGE"
			type="OUTLINE_GRAY"
			i18n-label="@@integracao-rdstation:label-btn-salvar-alteracoes"
			label="Salvar alterações"
			style="margin-right: 15px"
			(click)="salvarIntegracao()"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #traducao>
	<span xingling="copy-sucess" i18n="@@integracao-wordpress:copy-sucess">
		Link copiado para área de transferencia!
	</span>
	<span xingling="copy-error" i18n="@@integracao:copy-error">
		Falha ao tentar copiar Link!
	</span>
	<span xingling="ativada-com-sucesso" i18n="@@integracoes:ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		xingling="inativada-com-sucesso"
		i18n="@@integracoes:inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
	<span xingling="salva-com-sucesso" i18n="@@integracoes:salva-com-sucesso">
		Integração salva com sucesso!
	</span>
	<span
		xingling="aprovada-com-sucesso"
		i18n="@@integracoes:aprovada-com-sucesso">
		Integração aprovada com sucesso!
	</span>
</pacto-traducoes-xingling>
