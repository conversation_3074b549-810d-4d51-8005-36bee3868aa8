import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { AdmCoreApiBaseService } from "./adm-core-api-base.service";
import { AdmCoreApiModule } from "./adm-core-api.module";
import { map } from "rxjs/operators";

@Injectable({
	providedIn: "root",
})
export class AdmCoreApiArmarioService {
	constructor(private restApi: AdmCoreApiBaseService) {}

	public getArmarioByPessoa(codigoPessoa: number | string): Observable<any[]> {
		return this.restApi
			.get<any>(`aluguel-armario/by-pessoa/${codigoPessoa}`)
			.pipe(map((result) => result));
	}
}
