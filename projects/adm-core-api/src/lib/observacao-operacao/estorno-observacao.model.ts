import { Pessoa } from "../cliente.model";
import { MovParcela } from "../mov-parcela.model";

export interface ObservacaoOperacaoTotais {
	observacoes: Array<ObservacaoOperacao>;
	valorTotalParcelas: number;
}

export interface ObservacaoOperacao {
	codigo: number;
	justificativa: string;
	dataOperacao: Date;
	tipoObservacao: string;
	movParcela: MovParcela;
	valorOperacao: number;
	usuarioResponsavel: string;
	tipoCancelamento: string;
	pessoa: Pessoa;
	codigoCliente: number;
	matriculaCliente: string;
	codigoMatriculaCliente: number;
}
