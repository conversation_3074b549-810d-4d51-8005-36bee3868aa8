import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import {
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoRecurso,
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "treino-api";
import { PactoLayoutSDKWrapper } from "../../../../../sdk-wrapper/sdk-wrappers";
import { LayoutNavigationService } from "../../../../layout-navigation.service";
import { PlataformModuleConfig, PlatformMenuItem } from "../../../../models";
import { PermissaoService } from "../../../../permissao/permissao.service";
import { MenuConfigService } from "../../menu-config.service";

@Injectable({
	providedIn: "root",
})
export class AgendaCadastrosMenuConfigServiceService extends MenuConfigService {
	constructor(
		protected layoutNavigationService: LayoutNavigationService,
		private permissaoService: PermissaoService,
		private pactoLayoutSDKWrapper: PactoLayoutSDKWrapper
	) {
		super(layoutNavigationService);
	}

	get menus(): Observable<Array<PlatformMenuItem>> {
		return of([this.cadastroParentItem]);
	}

	get cadastroParentItem(): PlatformMenuItem {
		const permitido: boolean = this.permissaoService.temFuncionalidadeTreino(
			PerfilAcessoFuncionalidadeNome.UTILIZAR_MODULO_AGENDA
		);
		const menu: PlatformMenuItem = {
			id: "cadastros",
			inativo: !permitido,
			configMenuSidebar: {
				submenus: [
					this.configurarAulaMenuItem,
					this.configurarTurmaMenuItem,
					this.configurarDisponibilidadeMenuItem,
					this.configurarLocacaoMenuItem,
					this.tipoAgendamentoMenuItem,
				],
			},
			configMenuExplorar: {
				submenus: [
					this.configurarAulaMenuItem,
					this.configurarTurmaMenuItem,
					this.configurarDisponibilidadeMenuItem,
					this.configurarLocacaoMenuItem,
					this.tipoAgendamentoMenuItem,
				],
			},
		};

		this.setParentIdMenuSidebar(menu);
		this.setParentIdMenuExplorar(menu);

		return menu;
	}

	get configurarAulaMenuItem(): PlatformMenuItem {
		return {
			id: "configurar-aula",
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.CADASTRO_AULAS &&
				PerfilAcessoFuncionalidadeNome.CADASTROS_AGENDA &&
				PerfilAcessoFuncionalidadeNome.UTILIZAR_MODULO_AGENDA,
			permitido:
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.CADASTRO_AULAS
				) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.CADASTROS_AGENDA
				) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.UTILIZAR_MODULO_AGENDA
				),
			favoriteIdentifier: "AGENDA_CONFIGURAR_AULAS",
			module: PlataformModuleConfig.TREINO,
			route: {
				internalLink: "/agenda/aula",
			},
		};
	}

	get configurarLocacaoMenuItem(): PlatformMenuItem {
		return {
			id: "configurar-locacao",
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.CADASTRO_AULAS &&
				PerfilAcessoFuncionalidadeNome.CADASTROS_AGENDA &&
				PerfilAcessoFuncionalidadeNome.UTILIZAR_MODULO_AGENDA,
			permitido: this.permissaoService.temFuncionalidadeTreino(
				PerfilAcessoFuncionalidadeNome.CADASTRO_AULAS
			),
			favoriteIdentifier: "AGENDA_CONFIGURAR_AULAS",
			module: PlataformModuleConfig.TREINO,
			route: {
				internalLink: "/agenda/locacao",
			},
		};
	}

	get configurarTurmaMenuItem(): PlatformMenuItem {
		return {
			id: "configurar-turma",
			permissaoAdm: "5.12 - Turmas",
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.CADASTROS_AGENDA &&
				PerfilAcessoFuncionalidadeNome.UTILIZAR_MODULO_AGENDA,
			permitido:
				this.permissaoService.temRecursoAdm("5.12") &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.CADASTROS_AGENDA
				) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.UTILIZAR_MODULO_AGENDA
				),
			favoriteIdentifier: "AGENDA_CONFIGURAR_TURMAS",
			module: PlataformModuleConfig.TREINO,
			route: {
				internalLink: "/agenda/turma",
			},
		};
	}

	get configurarDisponibilidadeMenuItem(): PlatformMenuItem {
		return {
			id: "configurar-disponibilidade",
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.CADASTRO_AULAS &&
				PerfilAcessoFuncionalidadeNome.UTILIZAR_MODULO_AGENDA,
			permitido: this.permissaoService.temFuncionalidadeTreino(
				PerfilAcessoFuncionalidadeNome.UTILIZAR_MODULO_AGENDA
			),
			favoriteIdentifier: "AGENDA_CONFIGURAR_DISPONIBILIDADES",
			module: PlataformModuleConfig.TREINO,
			route: {
				internalLink: "/agenda/disponibilidade",
			},
		};
	}

	get tipoAgendamentoMenuItem(): PlatformMenuItem {
		const permissaoAcesso = new PerfilAcessoRecurso(
			PerfilAcessoRecursoNome.TIPO_EVENTO,
			[
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.INCLUIR,
			]
		);
		return {
			id: "tipo-agendamento",
			permissaoTreino: permissaoAcesso,
			funcionalidadeTreino:
				PerfilAcessoFuncionalidadeNome.CADASTROS_AGENDA &&
				PerfilAcessoFuncionalidadeNome.UTILIZAR_MODULO_AGENDA,
			permitido:
				this.permissaoService.temPermissaoTreino(permissaoAcesso) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.CADASTROS_AGENDA
				) &&
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.UTILIZAR_MODULO_AGENDA
				),
			favoriteIdentifier: "AGENDA_TIPOS_AGENDAMENTO",
			module: PlataformModuleConfig.TREINO,
			route: {
				internalLink: "/agenda/tipo-agendamento",
			},
		};
	}
}
