import { NavModuleI18n } from "ui-kit";

const configurarAulaMenuEs: NavModuleI18n = {
	"configurar-aula": {
		name: "Configurar Aulas",
		description: "Programar as Aulas",
	},
};

const configurarLocacaoMenuEs: NavModuleI18n = {
	"configurar-locacao": {
		name: "Configurar Locações",
		description: "Configurar as Locações",
	},
};

const configurarTurmaMenuEs: NavModuleI18n = {
	"configurar-turma": {
		name: "Configurar Turmas",
		description: "Programar as Turmas",
	},
};

const configurarDisponibilidadeMenuEs: NavModuleI18n = {
	"configurar-disponibilidade": {
		name: "Configurar Disponibilidades",
		description: "Programar as Disponibilidades",
	},
};

const tipoAgendamentoMenuEs: NavModuleI18n = {
	"tipo-agendamento": {
		name: "Tipos de Agendamento",
		description: "Configure os tipos de agendamento.",
	},
};

export const cadastroMenuEs: NavModuleI18n = {
	cadastros: {
		name: "Cadastros",
		description: `Cadastros`,
		searchTokens: "cadastro, cadastros",
	},
	...configurarAulaMenuEs,
	...configurarTurmaMenuEs,
	...configurarDisponibilidadeMenuEs,
	...tipoAgendamentoMenuEs,
};
