export interface Ticket {
	id: string;
	status?: string;
	justification?: string;
	baseStatus?: string;
	subject?: string;
	subjectDetail?: string;
	owner?: Owner;
	clients?: Array<Client>;
	clientPerson?: Client;
	createdDate?: Date;
	createdDateFromNow?: string;
	createdDateFormat?: string;
	expanded?: boolean;
	actions?: Array<Action>;
	serviceFirstLevelId?: number;
	serviceFirstLevel?: string;
	serviceSecondLevel?: string;
	serviceThirdLevel?: string;
	customFieldValues?: Array<CustomField>;
	nomeSolicitante?: string;
	jiraIssueKey?: string;
	statusHistories?: Array<StatusHistory>;
	statusJira?: string;
}

export interface CustomField {
	customFieldId: number;
	customFieldRuleId: number;
	line?: number;
	value?: string;
}

export interface Owner {
	id: string;
	businessName: string;
}

export interface Client {
	id: string;
	businessName: string;
	personType: number;
}

export interface Action {
	id?: number;
	type?: number;
	description?: string;
	status?: string;
	createdDate?: Date;
	createdDateFormated?: string;
	createdBy?: any;
	responsavel?: string;
	origin?: number;
}

export interface Person {
	id?: string;
	personType?: number;
	accessProfile?: string;
	businessName?: string;
	cpfCnpj?: string;
}

export interface StatusHistory {
	status?: string;
	justification?: string;
	changedDate?: Date;
}
