import { BiCardCicloDeVidaDoClienteComponent } from "@adm/modules/bi/bi-adm/components/bi-card-ciclo-de-vida-do-cliente/bi-card-ciclo-de-vida-do-cliente.component";
import { BiSharedModule } from "@adm/modules/bi/bi-shared/bi-shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BiCardCicloDeVidaDoClienteFilterComponent } from "./bi-card-cliclo-de-vida-do-cliente-filter/bi-card-ciclo-de-vida-do-cliente-filter.component";
import { BiModalLtvInfosComponent } from "./bi-modal-ltv-infos/bi-modal-ltv-infos.component";

@NgModule({
	declarations: [
		BiCardCicloDeVidaDoClienteComponent,
		BiCardCicloDeVidaDoClienteFilterComponent,
		BiModalLtvInfosComponent,
	],
	imports: [CommonModule, ReactiveFormsModule, BiSharedModule],
	exports: [BiCardCicloDeVidaDoClienteComponent],
	entryComponents: [
		BiCardCicloDeVidaDoClienteFilterComponent,
		BiModalLtvInfosComponent,
	],
})
export class BiCardCicloDeVidaDoClienteModule {}
