import { ListItem, TabData } from "@adm/modules/bi/bi-adm/bi-adm.model";
import { BiCardCobrancaConvenioFilterComponent } from "@adm/modules/bi/bi-adm/components/bi-card-cobranca-convenio/bi-card-cobranca-convenio-filter/bi-card-cobranca-convenio-filter.component";
import { BiCardWithModalsBase } from "@adm/modules/bi/bi-shared/base/bi-card/bi-card-with-modals-base";
import { ConhecimentoEnum } from "@adm/modules/bi/bi-shared/models/bi-mdl-ajuda.model";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BiMdlAjudaService } from "@adm/modules/bi/bi-shared/services/bi-mdl-ajuda.service";
import { BiSidenavService } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe, DatePipe, DecimalPipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
} from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { Router } from "@angular/router";
import { ApexOptions } from "apexcharts";
import {
	BiMsApiRecorrenciaService,
	DadosRecorrenciaBiInadimplenciaJSON,
	DadosRecorrenciaItemJSON,
	DadosRecorrenciaTotalizadorJSON,
	FiltroRecorrencia,
	RecorrenciaResponseModel,
	TipoBIDCC,
} from "bi-ms-api";
import { ApexTooltip } from "ng-apexcharts";
import { ToastrService } from "ngx-toastr";
import { takeUntil } from "rxjs/operators";
import { PactoDataTableStateManager } from "ui-kit";

@Component({
	selector: "adm-bi-card-cobranca-convenio",
	templateUrl: "./bi-card-cobranca-convenio.component.html",
	styleUrls: [
		"./bi-card-cobranca-convenio.component.scss",
		"../../../bi-shared/bi-shared.scss",
	],
})
export class BiCardCobrancaConvenioComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnDestroy
{
	loading: boolean = false;
	tabData: TabData;
	tableStateTotalizadorParcelas: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	tableStateTotalizadorAguardandoRetorno: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	showHiddenFields: boolean;
	chartOptions: ApexOptions = {
		series: [
			{
				name: "Enviadas com retorno",
				data: [69, 35, 51, 49, 22, 69],
			},
			{
				name: "Recebido",
				data: [10, 41, 35, 12, 49, 44],
			},
			{
				name: "A Receber",
				data: [10, 20, 35, 51, 49, 22],
			},
			{
				name: "Eficiência (%)",
				data: [10, 20, 35, 51, 49, 22],
			},
			{
				name: "Inadimplência(%)",
				data: [10, 20, 35, 51, 49, 22],
			},
		],
		xaxis: {
			categories: [
				"02/2024",
				"03/2024",
				"04/2024",
				"05/2024",
				"06/2024",
				"07/2024",
			],
		},
	};

	tooltipChartResultadoDCC: ApexTooltip;

	dataLabelsChart: ApexDataLabels = {
		enabled: true,
		enabledOnSeries: [0, 1],
		textAnchor: "middle",
	};

	constructor(
		protected breakpointObserver: BreakpointObserver,
		protected cd: ChangeDetectorRef,
		protected currencyPipe: CurrencyPipe,
		protected decimalPipe: DecimalPipe,
		protected datePipe: DatePipe,
		protected dialog: MatDialog,
		protected router: Router,
		protected biCommonService: BiCommonService,
		private biMdlAjudaService: BiMdlAjudaService,
		private biSidenavService: BiSidenavService,
		private toastrService: ToastrService,
		private biRecorrenciaService: BiMsApiRecorrenciaService
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init();
		this._buildTooltip();
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this.loadData(this.reloadFull);
	}

	ngOnDestroy() {
		super.destroy();
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Cobrança de convênio",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-cobrancas-por-convenio-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	openSideNav() {
		const ref = this.biSidenavService.open(
			BiCardCobrancaConvenioFilterComponent,
			{
				globalFilter: this.globalFilterForm.value,
				filters: this.filtros,
			}
		);

		ref.afterClosed.pipe(takeUntil(this.destroy$)).subscribe((data) => {
			if (data && data.filters) {
				Object.assign(this.filtros, data.filters);
				this.loadData(true);
			}
		});
	}

	loadData(reloadFull?: boolean) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		}
		if (!this.withFakeData) {
			this._loadDataApi(reloadFull);
		}
	}

	private _loadDataApi(reloadFull) {
		this.loading = true;
		this.cd.detectChanges();

		const inicio = new Date(
			this.filtros.data.getFullYear(),
			this.filtros.data.getMonth(),
			this.filtros.data.getDate(),
			0,
			0,
			0,
			0
		);
		this.filtros.dataInicio = inicio;
		this.tableStateTotalizadorParcelas.patchState({ loading: true });
		this.tableStateTotalizadorAguardandoRetorno.patchState({ loading: true });
		this.biRecorrenciaService
			.list({
				filtroRecorrencia: {
					atualizarAgora: reloadFull,
					empresas: [this.filtros.empresa],
					colaboradores: this.filtros.colaboradores,
					convenios: this.filtros.convenios
						? this.filtros.convenios.map((c) => c.codigo)
						: null,
					dataBase: this.filtros.dataInicio.getTime(),
					somenteParcelasMes: this.filtros.somenteParcelasMes,
					incluirContratosCancelados: this.filtros.incluirContratosCancelados,
					somenteParcelasForaMes: this.filtros.somenteParcelasForaMes,
				} as FiltroRecorrencia,
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v: RecorrenciaResponseModel) => {
					this._populateTabData(v);
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.tableStateTotalizadorParcelas.patchState({ loading: false });
					this.tableStateTotalizadorAguardandoRetorno.patchState({
						loading: false,
					});
					this.cd.detectChanges();
				},
			});
	}

	_populateTabData(v?: RecorrenciaResponseModel) {
		if (!v) {
			v = {
				itens: new Array<DadosRecorrenciaItemJSON>(),
				dadosInadimplencia: new Array<DadosRecorrenciaBiInadimplenciaJSON>(),
				totalizador: new Array<DadosRecorrenciaTotalizadorJSON>(),
				totalizadorAguardandoRetorno:
					new Array<DadosRecorrenciaTotalizadorJSON>(),
			};
		}

		const listItensFirst = v.itens
			.sort((a, b) => {
				if (a.tipo > b.tipo) {
					return 1;
				}
				if (a.tipo < b.tipo) {
					return -1;
				}

				return 0;
			})
			.map((item) => {
				const tipoEnum = TipoBIDCC.obterPorID(item.tipo);
				return {
					text: tipoEnum.descricao,
					tooltipText: tipoEnum.hint,
					value: item.qtd,
					monetaryValue: item.valor,
				} as ListItem;
			});

		this.tableStateTotalizadorParcelas.patchState({
			loading: false,
			data: v.totalizador,
		});

		this.tableStateTotalizadorAguardandoRetorno.patchState({
			loading: false,
			data: v.totalizadorAguardandoRetorno,
		});

		const enviadasComRetorno = v.dadosInadimplencia.map((g) => g.valorTotal);
		const recebido = v.dadosInadimplencia.map((g) => g.valorPago);
		const aReceber = v.dadosInadimplencia.map((g) => g.valorEmAberto);
		const eficienciaPerc = v.dadosInadimplencia.map(
			(g) => 100 - g.inadimplencia
		);
		const indimplenciaPerc = v.dadosInadimplencia.map((g) => g.inadimplencia);

		const maxValue = Math.max(
			...[...enviadasComRetorno, ...recebido, ...aReceber]
		);

		const yMax = maxValue === 0 ? 10 : maxValue * 1.2; // Add 20% padding if not zero

		this.chartOptions = {
			series: [
				{
					name: "Enviadas com retorno",
					type: "line",
					data: enviadasComRetorno,
				},
				{
					name: "Recebido",
					type: "line",
					data: recebido,
				},
				{
					name: "A Receber",
					type: "line",
					data: aReceber,
				},
				{
					name: "Eficiência (%)",
					type: "column",
					data: eficienciaPerc,
				},
				{
					name: "Inadimplência (%)",
					type: "column",
					data: indimplenciaPerc,
				},
			],
			xaxis: {
				type: "category",
				categories: v.dadosInadimplencia.map((g) => g.mes),
				tooltip: {
					enabled: false,
				},
			},
			yaxis: [
				{
					seriesName: "Enviadas com retorno",
					opposite: true,
					min: 0,
					max: yMax,
					forceNiceScale: true,
					labels: {
						formatter: (val, opts) => {
							if (isNaN(val)) {
								val = 0;
							}
							return val.toFixed(0);
						},
					},
				},
				{
					seriesName: "Enviadas com retorno",
					show: false,
				},
				{
					seriesName: "Enviadas com retorno",
					show: false,
				},
				{
					seriesName: "Eficiência (%)",
					min: 0,
					max: 100,
					stepSize: 20,
					tickAmount: 5,
					labels: {
						formatter: (val) => {
							return val.toFixed(0);
						},
					},
				},
				{
					seriesName: "Eficiência (%)",
					show: false,
				},
			],
		};
		this.tabData = {
			sections: [
				{
					listItens: listItensFirst,
				},
			],
		};
	}

	mostrarMais() {
		this.showHiddenFields = !this.showHiddenFields;
	}

	private _buildTooltip() {
		const currencyPipe = this.currencyPipe;
		const decimalPipe = this.decimalPipe;

		this.tooltipChartResultadoDCC = {
			y: {
				formatter(val: number, opts?: any): string {
					switch (opts.seriesIndex) {
						case 0:
						case 1:
						case 2:
							return currencyPipe.transform(val, "BRL");
						case 3:
						case 4:
							return `${decimalPipe.transform(val, "1.2-2")}%`;
						default:
							return val.toString();
					}
				},
			},
		};
	}
}
