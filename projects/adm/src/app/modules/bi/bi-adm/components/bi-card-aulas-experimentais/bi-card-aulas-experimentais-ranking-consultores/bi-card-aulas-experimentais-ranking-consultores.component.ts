import { CurrencyPipe } from "@angular/common";
import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	HostBinding,
	Inject,
	LOCALE_ID,
	OnInit,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { DomSanitizer } from "@angular/platform-browser";
import { PactoDataGridConfig } from "ui-kit";
import { ModalRankingConsultoresComponent } from "./modal/modal-ranking-consultores.component";

@Component({
	selector: "adm-bi-card-aulas-experimentais-ranking-consultores",
	templateUrl:
		"./bi-card-aulas-experimentais-ranking-consultores.component.html",
	styleUrls: [
		"./bi-card-aulas-experimentais-ranking-consultores.component.scss",
	],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardAulasExperimentaisRankingConsultoresComponent
	implements OnInit
{
	@HostBinding("class.bi-card-aulas-experimentais-ranking-consultores")
	enableEncapsulation = true;

	@ViewChild("tableData", { static: true }) tableData;
	@ViewChild("celulaConsultor", { static: false })
	celularConsultor: TemplateRef<any>;

	table: PactoDataGridConfig;

	constructor(
		@Inject(LOCALE_ID) private locale,
		private cd: ChangeDetectorRef,
		private domSanitizer: DomSanitizer,
		private currencyPipe: CurrencyPipe,
		private dialog: MatDialog
	) {}

	ngOnInit() {}

	ngAfterViewInit() {
		this.initTable();
	}

	private initTable() {
		this.table = new PactoDataGridConfig({
			pagination: false,
			dataAdapterFn: (serverData) => {
				return {
					content: [
						{
							pessoa: {
								codigo: 1,
								nome: "Consulor 01",
								fotoUrl: this.domSanitizer.bypassSecurityTrustUrl(
									`pacto-ui/images/user-image-default.svg`
								),
							},
							faturamento: 0,
							icv: 0,
						},
						{
							pessoa: {
								codigo: 2,
								nome: "Consulor 02",
								fotoUrl: this.domSanitizer.bypassSecurityTrustUrl(
									`pacto-ui/images/user-image-default.svg`
								),
							},
							faturamento: 0,
							icv: 5.356,
						},
						{
							pessoa: {
								codigo: 3,
								nome: "Consulor 03",
								fotoUrl: this.domSanitizer.bypassSecurityTrustUrl(
									`pacto-ui/images/user-image-default.svg`
								),
							},
							faturamento: 0,
							icv: 0,
						},
					],
				};
			},
			columns: [
				{
					titulo: "Consultor",
					nome: "pessoa",
					visible: true,
					ordenavel: false,
					celula: this.celularConsultor,
				},
				{
					titulo: "Faturamento",
					nome: "faturamento",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => {
						if (v === undefined) {
							v = 0;
						}
						return this.currencyPipe.transform(
							v,
							"BRL",
							undefined,
							undefined,
							this.locale
						);
					},
				},
				{
					titulo: "ICV",
					nome: "icv",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => {
						if (!v) {
							return "00%";
						}
						return this.currencyPipe.transform(v, "", "", "1.2-2") + "%";
					},
				},
			],
		});
		this.cd.detectChanges();
	}

	openModalRankingConsultores(): void {
		const dialogRef = this.dialog.open(ModalRankingConsultoresComponent, {
			width: "1000px",
			height: "588px",
			autoFocus: false,
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (result === true) {
				this.tableData.reloadData();
			}
		});
	}
}
