import {
	AfterContentInit,
	Component,
	ContentChildren,
	HostBinding,
	Input,
	OnInit,
	QueryList,
	ViewEncapsulation,
} from "@angular/core";
import { CatExpansionPanelComponent } from "./cat-expansion-panel/cat-expansion-panel.component";

let uniqueId = 0;

@Component({
	selector: "adm-cat-accordion",
	templateUrl: "./cat-accordion.component.html",
	styleUrls: ["./cat-accordion.component.scss"],
})
export class CatAccordionComponent implements OnInit, AfterContentInit {
	@ContentChildren(CatExpansionPanelComponent)
	panels: QueryList<CatExpansionPanelComponent>;
	@Input() multi = false;
	@HostBinding("id")
	@Input()
	id: string;

	constructor() {}

	ngOnInit() {
		if (!this.id) {
			this.id = `cat-accordion-${uniqueId++}`;
		}
	}

	ngAfterContentInit() {
		if (this.panels) {
			this.panels.forEach((panel) => {
				panel.opened.subscribe((value) => {
					if (value) {
						this.panels.forEach((p) => {
							if (p.id !== panel.id && !this.multi) {
								p.expanded = false;
								p.headers.forEach((head) => (head.isOpen = false));
							}
						});
					}
				});
			});
		}
	}
}
