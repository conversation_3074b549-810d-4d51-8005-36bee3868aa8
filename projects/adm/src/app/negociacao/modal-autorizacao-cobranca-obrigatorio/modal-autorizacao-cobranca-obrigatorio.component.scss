@import "dist/ui-kit/assets/scss/cores.vars";
@import "dist/ui-kit/assets/ds3/colors.var";

:host {
	font-size: 14px;
}

.modal-title {
	display: flex;
	align-items: center;
	padding-left: 16px;
	padding-right: 16px;
	min-height: 52px;
	border-bottom: 1px solid $cinza03;

	.modal-warning-icon {
		margin-right: 10px;
		font-size: 30px;
		display: flex;
		align-items: center;
		color: $supportYellow04;
	}

	button {
		margin-left: auto;
	}
}

.modal-content {
	padding: 16px 16px 16px 16px;
	color: $typeDefaultText;

	.aviso-importante {
		i {
			margin-right: 8px;
			line-height: 20px;
			font-size: 20px;
		}

		span {
			text-align: left;
			margin-top: 0;
		}

		margin: 0 0 16px 0;
		display: inline-flex;
		align-items: center;
		color: $supportBlack06;
		font-size: 14px;
		line-height: 20px;
		padding: 8px 16px;
		background-color: #fdfdb4;
		border-radius: 5px;
	}
}

.modal-actions {
	display: flex;
	justify-content: end;
	padding-left: 16px;
	padding-right: 16px;
	padding-bottom: 16px;
}
