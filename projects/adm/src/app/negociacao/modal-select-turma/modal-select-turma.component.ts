import { ChangeDetectorR<PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { AgendaTurma } from "adm-core-api/lib/agenda-turma.model";
import { TraducoesXinglingComponent } from "ui-kit";
import { AgendaTurmaHorario } from "adm-core-api/lib/agenda-turma-horario.model";
import { HorarioTurmaNegociacao } from "adm-core-api/lib/horario-turma-negociacao.model";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { FormControl, FormGroup } from "@angular/forms";
import { zip } from "rxjs";
import { map } from "rxjs/operators";
import { AdmCoreApiNegociacaoService } from "adm-core-api";
import { NegociacaoService } from "../negociacao.service";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "adm-modal-select-turma",
	templateUrl: "./modal-select-turma.component.html",
	styleUrls: ["./modal-select-turma.component.scss"],
})
export class ModalSelectTurmaComponent implements OnInit {
	formGroup = new FormGroup({
		professor: new FormControl(0),
		nivel: new FormControl(0),
		periodo: new FormControl("todo"),
		disponibilidade: new FormControl("disponiveis"),
	});
	configEmpresa;
	modalidade;
	configs;
	validado = false;
	aulasCheias;
	idadeEmMeses;
	nivelAluno;
	inicio;
	cliente;
	agenda: Array<AgendaTurma> = [];
	limiteVezesSemana = 0;
	turmasSelecionadas: Array<HorarioTurmaNegociacao> = [];
	diasSemana = [
		"SEGUNDA_FEIRA",
		"TERCA_FEIRA",
		"QUARTA_FEIRA",
		"QUINTA_FEIRA",
		"SEXTA_FEIRA",
		"SABADO",
		"DOMINGO",
	];
	integracaoMgb: any;
	horarioNivelMgbSelecionado = "";
	@ViewChild("diaSemanaTexto", { static: true })
	xingling: TraducoesXinglingComponent;

	constructor(
		private cd: ChangeDetectorRef,
		private dialog: NgbActiveModal,
		private service: AdmCoreApiNegociacaoService,
		private snotifyService: SnotifyService,
		private negociacaoState: NegociacaoService
	) {}

	ngOnInit() {
		this.formGroup.valueChanges.subscribe((value) => {
			this.service
				.agendaTurmas(
					this.modalidade,
					value.nivel ? value.nivel : 0,
					value.professor ? value.professor : 0,
					value.periodo && value.periodo !== "" ? value.periodo : "-",
					value.disponibilidade && value.disponibilidade !== ""
						? value.disponibilidade
						: "-",
					this.cliente ? this.cliente.codigo : null,
					this.inicio
				)
				.subscribe((data) => {
					this.agenda = data;
					this.cd.detectChanges();
				});
		});
		setTimeout(() => {
			this.cd.detectChanges();
		}, 200);
	}

	get niveis() {
		const niveis =
			this.configs && this.configs.niveis ? this.configs.niveis : [];
		return [{ codigo: 0, descricao: "Nível" }, ...niveis];
	}

	temNivel(turma) {
		return (
			!turma.niveis ||
			turma.niveis.length === 0 ||
			turma.niveis.indexOf(this.nivelAluno) > -1
		);
	}

	get professores() {
		const professores =
			this.configs && this.configs.professores ? this.configs.professores : [];
		return [{ codigo: 0, nome: "Professor" }, ...professores];
	}

	get periodos() {
		return [
			{ id: "todo", nome: "Dia todo" },
			{ id: "MANHA", nome: "Manhã" },
			{ id: "TARDE", nome: "Tarde" },
			{ id: "NOITE", nome: "Noite" },
		];
	}

	get disponibilidades() {
		return [
			{ id: "todos", nome: "Todos os horários" },
			{ id: "disponiveis", nome: "Horários disponíveis" },
			{ id: "indisponiveis", nome: "Horários indisponíveis" },
		];
	}

	primeiroNome(nome: string) {
		const nameParts = nome.toLowerCase().split(" ");
		return nameParts[0];
	}

	turmaSelecionada(turma: HorarioTurmaNegociacao) {
		return this.turmasSelecionadas.some(
			(objeto) => objeto.codigo === turma.codigo
		);
	}

	turmaMesmaAnterior(turma: HorarioTurmaNegociacao): boolean {
		if (
			this.configEmpresa &&
			this.configEmpresa.habilitarValidacaoHorariosMesmaTurma
		) {
			if (
				this.turmasSelecionadas.some((objeto) => objeto.turma !== turma.turma)
			) {
				this.snotifyService.error(
					"Só é permitido selecionar horários da mesma turma."
				);
				return false;
			} else {
				return true;
			}
		}
		return true;
	}

	periodo(turma: HorarioTurmaNegociacao) {
		return turma.inicio.replace(":", "h") + " - " + turma.fim.replace(":", "h");
	}

	clickTurma(diaSemana, turma: HorarioTurmaNegociacao) {
		if (this.turmaMesmaAnterior(turma)) {
			turma.diaSemana = this.xingling.getLabel(diaSemana);
			if (this.turmaSelecionada(turma)) {
				this.turmasSelecionadas = this.turmasSelecionadas.filter(
					(objeto) => objeto.codigo !== turma.codigo
				);
				this.validado = false;
				this.aulasCheias = [];
				if (
					this.integracaoMgb &&
					this.integracaoMgb.integrado &&
					this.turmasSelecionadas.length === 0
				) {
					this.horarioNivelMgbSelecionado = "";
				}
			} else {
				if (!this.temNivel(turma)) {
					return;
				}
				if (
					this.idadeEmMeses < turma.idadeMinimaMeses ||
					(turma.idadeMaximaMeses > 0 &&
						this.idadeEmMeses > turma.idadeMaximaMeses)
				) {
					return;
				}
				if (
					(turma.bloquearMatriculasAcimaLimite === true &&
						turma.capacidade <= turma.ocupacao) ||
					this.selecionouTodos
				) {
					return;
				}
				if (
					this.integracaoMgb &&
					this.integracaoMgb.integrado &&
					!this.isMesmoNivelMgbSelecionado(turma)
				) {
					return;
				}
				this.turmasSelecionadas.push(turma);
				if (
					this.integracaoMgb &&
					this.integracaoMgb.integrado &&
					this.horarioNivelMgbSelecionado === ""
				) {
					this.horarioNivelMgbSelecionado = turma.nivelCodigoMgb;
				}
			}
		}
		this.cd.detectChanges();
	}

	isMesmoNivelMgbSelecionado(turma: HorarioTurmaNegociacao) {
		// caso integrado com mgb:
		// verificar se o horário selecionado agora possui o mesmo nível mgb já selecionado anteriormente
		// se o nivelCodigoMgb do horario selecionado estiver vazio indica que não possui vínculo com um nível mgb, e pode ser selecionado
		try {
			return (
				turma.nivelCodigoMgb === "" ||
				this.horarioNivelMgbSelecionado === "" ||
				turma.nivelCodigoMgb === this.horarioNivelMgbSelecionado
			);
		} catch (e) {
			console.error(e);
			return true;
		}
	}

	confirmarSelecao() {
		const horarios = [];
		this.aulasCheias = [];
		this.turmasSelecionadas.forEach((t) => {
			horarios.push(t.codigo);
		});

		if (horarios.length == 0 || this.validado) {
			this.dialog.close(this.turmasSelecionadas);
			this.cd.detectChanges();
			return;
		}

		this.service
			.validarAulasFixar(
				this.negociacaoState.getConfigsContrato().cliente,
				this.negociacaoState.getConfigsContrato().duracao,
				horarios
			)
			.subscribe((data) => {
				if (data && data.aulasCheias && data.aulasCheias.length > 0) {
					this.validado = true;
					this.aulasCheias = data.aulasCheias;
					this.cd.detectChanges();
					return;
				}
				this.dialog.close(this.turmasSelecionadas);
				this.cd.detectChanges();
			});
	}

	limparSelecao() {
		this.turmasSelecionadas = [];
		this.horarioNivelMgbSelecionado = "";
		this.cd.detectChanges();
	}

	ocupacao(turma: HorarioTurmaNegociacao) {
		if (turma.coletiva) {
			return (turma.capacidade < 10 ? "0" : "") + turma.capacidade;
		}
		let ocupacao = turma.ocupacao < 10 ? "0" : "";
		ocupacao += turma.ocupacao + "/";
		ocupacao += turma.capacidade < 10 ? "0" : "";
		ocupacao += turma.capacidade;
		return ocupacao;
	}

	get selecionouTodos(): boolean {
		return this.turmasSelecionadas.length === this.limiteVezesSemana;
	}

	get selecionouMais(): boolean {
		return this.turmasSelecionadas.length > this.limiteVezesSemana;
	}

	get isIntegradoMgb() {
		if (this.integracaoMgb && this.integracaoMgb.integrado) {
			return true;
		} else {
			return false;
		}
	}
}
