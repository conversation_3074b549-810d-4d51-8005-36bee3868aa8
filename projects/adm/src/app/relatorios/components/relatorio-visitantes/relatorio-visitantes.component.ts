import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ElementRef,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { AbstractControl, FormControl, FormGroup } from "@angular/forms";
import {
	FiltroCheckboxComponent,
	PactoDataGridConfig,
	RelatorioComponent,
	SelectFilterParamBuilder,
} from "ui-kit";
import { Api, PerfilAcessoRecurso, SessionService } from "sdk";
import { AdmRestService } from "../../../adm-rest.service";
import { RelatorioApiColaboradorService } from "relatorio-api";
import {
	Colaborador,
	Relatoriovisitantes,
} from "../../model/relatoriovisitantes.model";
import { SituacaoBvEnum } from "../../model/situacaobv-enum";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";
import { DataFiltro } from "ui-kit";
import { RelatorioApiEmpresaService } from "relatorio-api";
import { Empresa } from "../../../planos/plano.model";

declare var moment;

@Component({
	selector: "adm-relatorio-visitantes",
	templateUrl: "./relatorio-visitantes.component.html",
	styleUrls: ["./relatorio-visitantes.component.scss"],
})
export class RelatorioVisitantesComponent implements OnInit, AfterViewInit {
	@ViewChild("filtrosConsultores", { static: false })
	filtrosConsultores: FiltroCheckboxComponent;
	@ViewChild("tableVisitantesComponent", { static: false })
	tableVisitantesComponent: RelatorioComponent;
	@ViewChild("columnMatricula", { static: true })
	columnMatricula: TemplateRef<any>;
	@ViewChild("columnNome", { static: true }) columnNome: TemplateRef<any>;
	@ViewChild("columnTelefone", { static: true })
	columnTelefone: TemplateRef<any>;
	@ViewChild("columnEmail", { static: true }) columnEmail: TemplateRef<any>;
	@ViewChild("columnCadastro", { static: true })
	columnCadastro: TemplateRef<any>;
	@ViewChild("columnNascimento", { static: true })
	columnNascimento: TemplateRef<any>;
	@ViewChild("columnSituacao", { static: true })
	columnSituacao: TemplateRef<any>;
	@ViewChild("columnSexo", { static: true }) columnSexo: TemplateRef<any>;
	@ViewChild("columnLogradouro", { static: true })
	columnLogradouro: TemplateRef<any>;
	@ViewChild("columnNumero", { static: true }) columnNumero: TemplateRef<any>;
	@ViewChild("columnBairro", { static: true }) columnBairro: TemplateRef<any>;
	@ViewChild("columnCidade", { static: true }) columnCidade: TemplateRef<any>;
	@ViewChild("columnCep", { static: true }) columnCep: TemplateRef<any>;
	@ViewChild("columnComplemento", { static: true })
	columnComplemento: TemplateRef<any>;
	@ViewChild("columnEstadoCivil", { static: true })
	columnEstadoCivil: TemplateRef<any>;
	@ViewChild("columnProfissao", { static: true })
	columnProfissao: TemplateRef<any>;
	@ViewChild("columnEmpresa", { static: true }) columnEmpresa: TemplateRef<any>;
	@ViewChild("scrollMe", { static: false })
	private myScrollContainer: ElementRef;
	permissaoConsultarInfoTodasEmpresas =
		this.sessionService.perfilUsuario.funcionalidades.get(
			"consultarinfotodasempresas"
		);
	baseFilter: DataFiltro = {};
	filters: any;
	recurso: PerfilAcessoRecurso;
	tableVisitantes: PactoDataGridConfig;
	formGroup: FormGroup = new FormGroup({
		empresa: new FormControl(),
		datainiciobv: new FormControl(),
		datafimbv: new FormControl(),
		situacaoBv: new FormControl(),
		checkAllForm: new FormControl(),
		searchForm: new FormControl(),
	});
	formsControlsChecksBox: Array<{ id: number; form: FormControl }> = new Array<{
		id: number;
		form: FormControl;
	}>();
	consultores = new Array<any>();
	empresaLogada: any;
	situacoesBvs: any[];
	inicioMesAtual = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
	fimMesAtual = new Date(
		new Date().getFullYear(),
		new Date().getMonth() + 1,
		0
	);
	visitantes: {
		content: Array<Relatoriovisitantes>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<Relatoriovisitantes>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	consultoresData: {
		content: Array<Colaborador>;
		totalElements: 0;
		totalPages: 0;
		numberOfElements: 0;
		size: 0;
		number: 0;
	} = {
		content: new Array<Colaborador>(),
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		number: 0,
	};
	paginacaoConsultores = {
		size: 16,
		page: 0,
		sort: "ASC",
	};
	searchTermConsultores: string;
	mostarTabelaVistantes: boolean;

	constructor(
		private router: Router,
		private admRest: AdmRestService,
		private sessionService: SessionService,
		private colaboradorService: RelatorioApiColaboradorService,
		private cd: ChangeDetectorRef,
		public relatorioApiEmpresaService: RelatorioApiEmpresaService
	) {
		this.recurso = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.EMPRESA
		);
	}

	ngOnInit() {
		this.initSituacoesBv();
		this.initForm();
	}

	ngAfterViewInit() {
		this.createTableVisitantes();
		this.cd.detectChanges();
	}

	voltarAdm() {
		this.router.navigate(["adm"]);
	}

	initForm() {
		this.empresaLogada = {
			codigo: this.sessionService.empresaId,
			nome: this.sessionService.currentEmpresa.nome,
		};
		this.formGroup.patchValue({
			empresa: this.empresaLogada,
			datainiciobv: this.inicioMesAtual,
			datafimbv: this.fimMesAtual,
			situacaoBv: "-",
		});
		this.consultarConsultores();
		if (this.permissaoConsultarInfoTodasEmpresas) {
			this.formGroup.get("empresa").valueChanges.subscribe((value) => {
				this.paginacaoConsultores.page = 0;
				this.paginacaoConsultores.size = 16;
				this.formGroup.get("checkAllForm").setValue(false);
				this.formGroup.get("searchForm").setValue("");
				this.consultarConsultores();
			});
		} else {
			this.formGroup.get("empresa").disable();
		}
	}

	consultarVisitantes() {
		this.mostarTabelaVistantes = true;
		const dataInicioBv = new Date(this.formGroup.get("datainiciobv").value);
		const dataFimBv = new Date(this.formGroup.get("datafimbv").value);
		this.filters = {
			empresa: this.formGroup.get("empresa").value.codigo,
			datainiciobv: dataInicioBv.getTime(),
			datafimbv: dataFimBv.getTime(),
			situacaoBv: this.formGroup.get("situacaoBv").value,
			consultores: this.getConsultoresSelecionados(),
		};
		this.baseFilter.filters = this.filters;
		setTimeout(() => {
			this.tableVisitantesComponent.reloadData();
			this.scrollToComponent("tableVisitantesComponent");
		});
	}

	cleanFilters() {
		this.formGroup.get("empresa").setValue({
			codigo: this.sessionService.empresaId,
			nome: this.sessionService.currentEmpresa.nome,
		});
		this.formGroup.get("datainiciobv").setValue(undefined);
		this.formGroup.get("datafimbv").setValue(undefined);
		this.formGroup.get("situacaoBv").setValue("-");
		this.formGroup.get("searchForm").setValue("");
		this.formGroup.get("checkAllForm").setValue(false);
		this.formsControlsChecksBox.forEach((f) => f.form.setValue(false));
		this.cd.detectChanges();
	}

	private createTableVisitantes() {
		this.tableVisitantes = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrl(
				"/relatorio-de-visitantes",
				false,
				Api.MSRELATORIO
			),
			quickSearch: false,
			ghostLoad: false,
			ghostAmount: 5,
			showFilters: false,
			columns: [
				{
					nome: "matricula",
					titulo: this.columnMatricula,
					visible: false,
					ordenavel: true,
					exportNoVisible: true,
				},
				{
					nome: "nome",
					titulo: this.columnNome,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "telefones",
					titulo: this.columnTelefone,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "email",
					titulo: this.columnEmail,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "cadastro",
					titulo: this.columnCadastro,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "nascimento",
					titulo: this.columnNascimento,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "situacao",
					titulo: this.columnSituacao,
					visible: false,
					ordenavel: true,
					exportNoVisible: true,
				},
				{
					nome: "sexo",
					titulo: this.columnSexo,
					visible: false,
					ordenavel: true,
					exportNoVisible: true,
				},
				{
					nome: "logradouro",
					titulo: this.columnLogradouro,
					visible: false,
					ordenavel: true,
					exportNoVisible: true,
				},
				{
					nome: "numero",
					titulo: this.columnNumero,
					visible: false,
					ordenavel: true,
					exportNoVisible: true,
				},
				{
					nome: "bairro",
					titulo: this.columnBairro,
					visible: false,
					ordenavel: true,
					exportNoVisible: true,
				},
				{
					nome: "cidade",
					titulo: this.columnCidade,
					visible: false,
					ordenavel: true,
					exportNoVisible: true,
				},
				{
					nome: "cep",
					titulo: this.columnCep,
					visible: false,
					ordenavel: true,
					exportNoVisible: true,
				},
				{
					nome: "complemento",
					titulo: this.columnComplemento,
					visible: false,
					ordenavel: true,
					exportNoVisible: true,
				},
				{
					nome: "estadoCivil",
					titulo: this.columnEstadoCivil,
					visible: false,
					ordenavel: true,
					exportNoVisible: true,
				},
				{
					nome: "profissao",
					titulo: this.columnProfissao,
					visible: false,
					ordenavel: true,
					exportNoVisible: true,
				},
				{
					nome: "empresa",
					titulo: this.columnEmpresa,
					visible: true,
					ordenavel: true,
				},
			],
		});
	}

	empresaSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	initSituacoesBv() {
		const situacoes = [];
		situacoes.push({ label: "-", id: "-" });
		situacoes.push({ label: "Completo", id: SituacaoBvEnum.COMPLETO });
		situacoes.push({ label: "Incompleto", id: SituacaoBvEnum.INCOMPLETO });
		this.situacoesBvs = situacoes;
	}

	checksBoxChange(formsControlsChecksBox) {
		this.formsControlsChecksBox = formsControlsChecksBox;
	}

	scrollToComponent(idComponent) {
		const el = document.getElementById(idComponent);
		el.scrollIntoView();
	}

	private getConsultoresSelecionados(): string {
		let codigosConsultores = "";
		this.formsControlsChecksBox.forEach((f) => {
			if (f.form.value) {
				codigosConsultores += "," + f.id;
			}
		});
		codigosConsultores = codigosConsultores.replace(",", "");
		return codigosConsultores;
	}

	convertToFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	private consultarConsultores() {
		const empresaId =
			this.formGroup.get("empresa").value.codigo === null
				? 0
				: this.formGroup.get("empresa").value.codigo;
		const filtros = {
			empresa: empresaId,
			searchTerm: this.searchTermConsultores,
			tipoColaborador: ["CO"],
		};
		this.colaboradorService
			.findAll(filtros, this.paginacaoConsultores)
			.subscribe((response) => {
				this.consultoresData = response;
				this.filtrosConsultores.reloadData();
				this.cd.detectChanges();
			});
	}

	pageChangeConsultores(page) {
		this.paginacaoConsultores.page = page - 1;
		this.consultarConsultores();
	}

	pageSizeChangeConsultores(size) {
		this.paginacaoConsultores.size = size;
		this.consultarConsultores();
	}

	searchEvent(term: string) {
		this.searchTermConsultores = term;
		this.consultarConsultores();
	}

	checkAllConsultores(value) {
		const empresaId =
			this.formGroup.get("empresa").value === null
				? 0
				: this.formGroup.get("empresa").value.codigo;
		const filtros = {
			empresa: empresaId,
			searchTerm: this.searchTermConsultores,
			tipoColaborador: ["CO"],
		};
		const paginacao = {
			size: 0,
			page: 0,
			sort: "ASC",
		};
		this.colaboradorService
			.findAll(filtros, paginacao)
			.subscribe((response) => {
				response.content.forEach((item) => {
					const index = this.formsControlsChecksBox.findIndex(
						(f) => f.id === item.codigo
					);
					if (index === -1) {
						const form = new FormControl();
						form.setValue(value);
						this.formsControlsChecksBox.push({ id: item.codigo, form });
					}
				});
			});
	}

	getEmpresasDisponiveis(): Array<any> {
		const empresasDisponiveis = Object.assign([], this.sessionService.empresas);
		if (empresasDisponiveis.length > 1) {
			empresasDisponiveis.unshift({ codigo: null, nome: "-" });
		}
		return empresasDisponiveis;
	}
}
