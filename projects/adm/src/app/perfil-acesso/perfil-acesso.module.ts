import { NO_ERRORS_SCHEMA, NgModule } from "@angular/core";
import { Route, RouterModule } from "@angular/router";

import { CommonModule } from "@angular/common";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { SdkModule } from "sdk";
import { ComponentsModule, UiModule } from "ui-kit";
import { PerfilAcessoGuard } from "../guards/perfil-acesso.guard";
import { LayoutModule } from "../layout/layout.module";
import { PerfilAcessoCopiarPermissoesModalComponent } from "./components/perfil-acesso-copiar-permissoes-modal/perfil-acesso-copiar-permissoes-modal.component";
import { PerfilAcessoCreateModalComponent } from "./components/perfil-acesso-create-modal/perfil-acesso-create-modal.component";
import { PerfilAcessoEditComponent } from "./components/perfil-acesso-edit/perfil-acesso-edit.component";
import { PerfilAcessoListaComponent } from "./components/perfil-acesso-lista/perfil-acesso-lista.component";
import { PerfilAcessoReplicarEmpresasModalComponent } from "./components/perfil-acesso-replicar-empresas-modal/perfil-acesso-replicar-empresas-modal.component";
import { PerfilAcessoUsuariosPerfilModalComponent } from "./components/perfil-acesso-usuario-perfil-modal/perfil-acesso-usuario-perfil-modal.component";
import { PerfilAcessoService } from "./perfil-acesso.service";

const routes: Route[] = [
	{
		path: "",
		component: PerfilAcessoListaComponent,
		canActivate: [PerfilAcessoGuard],
	},
	{
		path: "novo",
		component: PerfilAcessoEditComponent,
		canActivate: [PerfilAcessoGuard],
	},
	{
		path: ":id",
		component: PerfilAcessoEditComponent,
		canActivate: [PerfilAcessoGuard],
	},
];

@NgModule({
	declarations: [
		PerfilAcessoListaComponent,
		PerfilAcessoEditComponent,
		PerfilAcessoCreateModalComponent,
		PerfilAcessoCopiarPermissoesModalComponent,
		PerfilAcessoReplicarEmpresasModalComponent,
		PerfilAcessoUsuariosPerfilModalComponent,
	],
	entryComponents: [
		PerfilAcessoCreateModalComponent,
		PerfilAcessoCopiarPermissoesModalComponent,
		PerfilAcessoReplicarEmpresasModalComponent,
		PerfilAcessoUsuariosPerfilModalComponent,
	],
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		UiModule,
		SdkModule,
		NgbModule,
		LayoutModule,
		ComponentsModule,
	],
	schemas: [NO_ERRORS_SCHEMA],
	providers: [PerfilAcessoService],
})
export class PerfilAcessoModule {}
