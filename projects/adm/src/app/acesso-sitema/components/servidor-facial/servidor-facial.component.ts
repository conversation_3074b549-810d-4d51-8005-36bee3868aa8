import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";

import { CadastroAuxApiServidorFacialService } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "sdk";

import {
	GridFilterConfig,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";

@Component({
	selector: "adm-servidor-facial",
	templateUrl: "./servidor-facial.component.html",
	styleUrls: ["./servidor-facial.component.scss"],
})
export class ServidorFacialComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnEmpresa", { static: true }) columnEmpresa: TemplateRef<any>;
	@ViewChild("tableDataComponent", { static: false })
	tableDataComponent: RelatorioComponent;
	tableData: PactoDataGridConfig;
	filterConfig: GridFilterConfig = { filters: [] };

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private servidorFacialService: CadastroAuxApiServidorFacialService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.initTable();
		this.initFilter();
		this.cd.detectChanges();
	}

	edit(servidorFacial) {
		if (
			this.sessionService.perfilUsuario.funcionalidades.get("servidorfacial")
		) {
			this.router.navigate([
				"adm",
				"acesso-sistema",
				"servidor-facial",
				servidorFacial.codigo,
			]);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("msg-usuario-sem-permissao")
			);
		}
	}

	delete(row: any) {
		if (
			this.sessionService.perfilUsuario.funcionalidades.get("servidorfacial")
		) {
			this.servidorFacialService.delete(row.codigo).subscribe(
				(response) => {
					this.notificationService.success(
						this.traducao.getLabel("success-excluded")
					);
					this.tableDataComponent.reloadData();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("msg-usuario-sem-permissao")
			);
		}
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	novo() {
		if (
			this.sessionService.perfilUsuario.funcionalidades.get("servidorfacial")
		) {
			this.router.navigate(["adm", "acesso-sistema", "novo-servidor-facial"]);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("msg-usuario-sem-permissao")
			);
		}
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (
			this.sessionService.perfilUsuario.funcionalidades.get("servidorfacial")
		) {
			if (event.iconName === "action-edit (key)") {
				this.edit(event.row);
			} else if (event.iconName === "action-delete (key)") {
				this.delete(event.row);
			}
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("msg-usuario-sem-permissao")
			);
		}
	}

	private initTable() {
		setTimeout(() => {
			this.tableData = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrlCadAux("servidor-facial"),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
						width: "26%",
					},
					{
						nome: "descricao",
						titulo: this.columnDescricao,
						visible: true,
						ordenavel: true,
						width: "32%",
					},
					{
						nome: "empresa",
						titulo: this.columnEmpresa,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => v.nome,
						width: "32%",
					},
				],
				actions: [
					{
						nome: this.traducao.getLabel("action-edit"),
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel(
							"tooltip-editar-servidor-facial"
						),
						actionFn: (row) => this.edit(row),
					},
					{
						nome: this.traducao.getLabel("action-delete"),
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel(
							"tooltip-excluir-servidor-facial"
						),
						actionFn: (row) => this.edit(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	private initFilter() {
		if (!this.tableData) {
			this.tableData = new PactoDataGridConfig({
				showFilters: true,
			});
		}
		this.filterConfig = {
			filters: [],
		};
	}
}
