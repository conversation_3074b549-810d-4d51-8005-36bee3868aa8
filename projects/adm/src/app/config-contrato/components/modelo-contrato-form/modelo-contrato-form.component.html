<adm-layout
	(goBack)="voltarParaListagem()"
	[pageTitle]="title"
	class="modelo-contrato-form"
	i18n-modulo="@@modelo-contrato:modulo"
	i18n-pageTitle="@@modelo-contrato:-acesso:title"
	modulo="Administrativo">
	<pacto-cat-tabs-transparent [actionLabel]="'Salvar'" [showAction]="">
		<ng-template
			i18n-label="@@plano:edit-label-dados-basicos"
			label="Dados Básicos"
			pactoTabTransparent>
			<pacto-cat-card-plain>
				<div class="table-wrapper pacto-shadow">
					<h1 class="titulo" i18n="@@modelo-contrato:insira-os-dados">
						Insira os dados
					</h1>
					<div class="row">
						<div class="col-md-2">
							<pacto-cat-form-input-number
								[formControl]="formGroup.get('codigo')"
								[id]="'codigo-modelo-contrato'"
								i18n-label="@@modelo-contrato:label-codigo"
								label="Código"
								placeholder="000"
								readonly="true"></pacto-cat-form-input-number>
						</div>
						<div class="col-md-6">
							<pacto-cat-form-input
								[control]="formGroup.get('descricao')"
								[id]="'descricao-modelo-contrato'"
								[maxlength]="45"
								errorMsg="Informe a descrição"
								i18n-errorMsg="@@modelo-contrato:error-descricao"
								i18n-label="@@modelo-contrato:label-descricao"
								i18n-placeholder="@@modelo-contrato:column-descricao"
								label="Descrição*"
								placeholder="Descrição"></pacto-cat-form-input>
						</div>
						<div class="col-md-4">
							<pacto-cat-form-select
								(change)="
									atualizarMarcadoresPorTipo(
										formGroup.get('tipoContrato').value
									)
								"
								[control]="formGroup.get('tipoContrato')"
								[id]="'tipo-modelo-contrato'"
								[items]="tipos"
								errorMsg="Informe o tipo"
								i18n-errorMsg="@@modelo-contrato:error-tipo"
								i18n-label="@@modelo-contrato:label-tipo"
								label="Tipo*"></pacto-cat-form-select>
						</div>
					</div>

					<div class="row">
						<div class="col-md-4">
							<pacto-cat-form-select
								[control]="formGroup.get('situacao')"
								[errorMsg]="'Informe a situação'"
								[id]="'situacao-modelo-contrato'"
								[items]="situacao"
								i18n-label="@@modelo-contrato:label-situacao"
								label="Situação*"></pacto-cat-form-select>
						</div>
						<div class="col-md-4">
							<pacto-cat-datepicker
								[className]="'datePicker'"
								[formControl]="formGroup.get('dataDefinicao')"
								i18n-label="@@modelo-contrato:label-data-definicao"
								label="Data definição*"></pacto-cat-datepicker>
						</div>
						<div class="col-md-4">
							<pacto-cat-form-input
								[control]="formGroup.get('responsavel')"
								[id]="'responsavel-modelo-contrato'"
								i18n-label="@@modelo-contrato:label-responsavel-definicao"
								i18n-placeholder="@@modelo-contrato:label-responsavel-definicao"
								label="Responsável definição"
								placeholder="Responsável definição"></pacto-cat-form-input>
						</div>
					</div>

					<div *ngIf="showMarcadores">
						<h1 class="titulo" i18n="@@modelo-contrato:insira-os-dados"></h1>
						<div class="row">
							<div class="col-md-4">
								<pacto-cat-form-select
									(change)="selecionarMarcador(marcadoresFC.value)"
									[control]="marcadoresFC"
									[id]="'marcadores-modelo-contrato'"
									[items]="marcadores"
									i18n-label="@@modelo-contrato:label-marcadores"
									label="Marcadores"></pacto-cat-form-select>
							</div>
							<div class="col-md-8 div-busca-rapida-marcador">
								<div class="search">
									<pacto-cat-form-input
										(keyup)="buscaRapidaMarcadores(buscaRapidaFC.value)"
										[control]="buscaRapidaFC"
										[id]="'busca-rapida-marcadores-modelo-contrato'"
										i18n-placeholder="@@label-busca-rapida"
										placeholder="Busca rápida..."></pacto-cat-form-input>
									<i class="pct pct-search"></i>
								</div>
							</div>
						</div>
						<div class="list-marcadores">
							<div
								*ngFor="let marcador of marcadorApresentado; let index = index"
								class="{{ getClassMarcador(index) }}">
								<i
									(click)="addMarcador(marcador.tag)"
									class="pct pct-plus-circle"
									id="marcador-{{ index }}"
									title="Adicionar tag"></i>
								<i
									(click)="copiarValorMarcador(marcador.tag)"
									class="pct pct-copy"
									id="marcador-{{ index }}"
									title="Copiar tag"></i>
								<div class="texto">{{ marcador.translation }}</div>
							</div>
						</div>
					</div>

					<div class="row">
						<div class="col-md-8 div-text-area-modelo-contrato">
							<quill-editor
								[formControl]="formGroup.get('texto')"
								[id]="'editor-texto-modelo-contrato'"
								[modules]="modules"
								[styles]="{ height: '400px' }"></quill-editor>
						</div>
					</div>

					<div class="row">
						<div id="informe-os-dados">
							<h1 i18n="@@modelo-contrato:informe-os-dados">
								Informe os dados
							</h1>
							<p i18n="@@modelo-contrato:informe-os-paramentros">
								Informe os parâmetros acima para
								{{ textoAcaoInforme }} um novo modelo de contrato.
							</p>
						</div>
					</div>
				</div>
				<div class="row justify-content-end div-btn">
					<pacto-cat-button
						(click)="voltarParaListagem()"
						i18n-label="@@modelo-contrato:btn-voltar"
						id="btn-voltar-lista-modelo-contrato"
						label="Voltar"
						size="LARGE"
						type="OUTLINE"></pacto-cat-button>
					<pacto-cat-button
						(click)="imprimir()"
						[iconPosition]="'after'"
						[icon]="'pct pct-printer'"
						i18n-label="@@modelo-contrato:btn-imprimir"
						id="btn-imprimir-modelo-contrato"
						label="Imprimir"
						size="LARGE"
						type="OUTLINE"></pacto-cat-button>
					<pacto-cat-button
						(click)="salvar()"
						[iconPosition]="'after'"
						[icon]="'pct pct-save'"
						i18n-label="@@modelo-contrato:btn-salvar"
						id="btn-salvar-modelo-contrato"
						label="Salvar"
						size="LARGE"
						type="PRIMARY"></pacto-cat-button>
				</div>
			</pacto-cat-card-plain>
		</ng-template>
		<ng-template
			*ngIf="permitirReplicarModeloContrato"
			i18n-label="@@plano:edit-label-dados-basicos"
			label="Replicar empresa"
			pactoTabTransparent>
			<adm-replicar-modelo-contrato
				*ngIf="permitirReplicarModeloContrato"
				[modeloContrato]="modeloContrato"></adm-replicar-modelo-contrato>
		</ng-template>
	</pacto-cat-tabs-transparent>
</adm-layout>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@modelo-contrato:PL" xingling="PL">Plano</span>
	<span i18n="@@modelo-contrato:SE" xingling="SE">Produto</span>
	<span i18n="@@modelo-contrato:CC" xingling="CC">Comprovante de Compra</span>
	<span i18n="@@modelo-contrato:AM" xingling="AM">Armário</span>
	<span i18n="@@modelo-contrato:VO" xingling="VO">Termo de Aceite</span>
	<span i18n="@@modelo-contrato:LP" xingling="LP">
		Termo de Aceite Link Pagamento
	</span>

	<span i18n="@@modelo-contrato:AT" xingling="AT">Ativo</span>
	<span i18n="@@modelo-contrato:IN" xingling="IN">Inativo</span>

	<span i18n="@@modelo-contrato:cliente" xingling="cliente">Cliente</span>
	<span i18n="@@modelo-contrato:contrato" xingling="contrato">Contrato</span>
	<span i18n="@@modelo-contrato:plano" xingling="plano">Plano</span>
	<span i18n="@@modelo-contrato:modalidade" xingling="modalidade">
		Modalidade
	</span>
	<span i18n="@@modelo-contrato:turma" xingling="turma">Turma</span>
	<span i18n="@@modelo-contrato:mov-parcela" xingling="mov-parcela">
		Mov. Parcela
	</span>
	<span i18n="@@modelo-contrato:recibo" xingling="recibo">Recibo</span>
	<span i18n="@@modelo-contrato:mov-pagamento" xingling="mov-pagamento">
		Mov. Pagamento
	</span>
	<span i18n="@@modelo-contrato:pacote" xingling="pacote">Pacote</span>
	<span i18n="@@modelo-contrato:usuario" xingling="usuario">Usuário</span>
	<span i18n="@@modelo-contrato:empresa" xingling="empresa">Empresa</span>
	<span i18n="@@modelo-contrato:venda" xingling="venda">Venda</span>
	<span i18n="@@modelo-contrato:itens-venda" xingling="itens-venda">
		Itens Venda
	</span>
	<span i18n="@@modelo-contrato:pacote-venda" xingling="pacote-venda">
		Pacote Venda
	</span>

	<span i18n="@@modelo-contrato:saved-success" xingling="saved-success">
		Modelo de contrato salvo com sucesso!
	</span>
	<span i18n="@@modelo-contrato:values-required" xingling="values-required">
		Para prosseguir é necessário que todos os campos obrigatórios sejam
		preenchidos!
	</span>

	<span
		i18n="@@modelo-contrato:values-required"
		xingling="msg-usuario-sem-permissao-editar">
		Seu usuário não possui a permissão para editar, procure o administrador.
	</span>
	<span
		i18n="@@modelo-contrato:values-required"
		xingling="msg-usuario-sem-permissao-cadastrar">
		Seu usuário não possui a permissão para cadastrar, procure o administrador.
	</span>
	<span
		i18n="@@modelo-contrato:contrato-prestacao-servico"
		xingling="contrato-prestacao-servico">
		CONTRATO DE PRESTAÇÃO DE SERVIÇOS
	</span>

	<!-- Tags translation -->

	<span i18n="@@modelo-contrato:assinaturadigital" xingling="assinaturadigital">
		AssinaturaDigital
	</span>
	<span
		xingling="assinaturadigitalrespfinanceiro"
		i18n="@@modelo-contrato:assinaturadigitalrespfinanceiro">
		AssinaturaDigitalRespFinanceiro
	</span>
	<span i18n="@@modelo-contrato:matricula-cliente" xingling="matricula-cliente">
		Matricula_CLiente
	</span>
	<span
		i18n="@@modelo-contrato:assinaturabiometriadigital"
		xingling="assinaturabiometriadigital">
		AssinaturaBiometriaDigital
	</span>
	<span i18n="@@modelo-contrato:banco-cliente" xingling="banco-cliente">
		Banco_CLiente
	</span>
	<span i18n="@@modelo-contrato:agencia-cliente" xingling="agencia-cliente">
		Agencia_CLiente
	</span>
	<span i18n="@@modelo-contrato:conta-cliente" xingling="conta-cliente">
		Conta_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:nowlocationipvonline_cliente"
		xingling="nowlocationipvonline_cliente">
		NowLocationIpVOnline_Cliente
	</span>
	<span i18n="@@modelo-contrato:webpage-cliente" xingling="webpage-cliente">
		Webpage_Cliente
	</span>
	<span i18n="@@modelo-contrato:sexo-cliente" xingling="sexo-cliente">
		Sexo_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:naturalidade-cliente"
		xingling="naturalidade-cliente">
		Naturalidade_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:estadocivil-cliente"
		xingling="estadocivil-cliente">
		EstadoCivil_Cliente
	</span>
	<span i18n="@@modelo-contrato:rg-cliente" xingling="rg-cliente">
		Rg_Cliente
	</span>
	<span i18n="@@modelo-contrato:cpf-cliente" xingling="cpf-cliente">
		Cpf_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:cpf-responsavel-legal-cliente"
		xingling="cpf-responsavel-legal-cliente">
		Cpf_ResponsavelLegal_Cliente
	</span>
	<span i18n="@@modelo-contrato:datanasc-cliente" xingling="datanasc-cliente">
		DataNasc_Cliente
	</span>
	<span i18n="@@modelo-contrato:nome-cliente" xingling="nome-cliente">
		Nome_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:nome-responsavel-legal-cliente"
		xingling="nome-responsavel-legal-cliente">
		Nome_ResponsavelLegal_Cliente
	</span>
	<span i18n="@@modelo-contrato:codigo-cliente" xingling="codigo-cliente">
		Codigo_Cliente
	</span>
	<span i18n="@@modelo-contrato:telefone-cliente" xingling="telefone-cliente">
		Telefone_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:telefone-celular-cliente"
		xingling="telefone-celular-cliente">
		Telefone_Celular_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:telefone-comercial-cliente"
		xingling="telefone-comercial-cliente">
		Telefone_Comercial_Cliente
	</span>
	<span i18n="@@modelo-contrato:endereco-cliente" xingling="endereco-cliente">
		Endereco_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:endereco-numero-cliente"
		xingling="endereco-numero-cliente">
		Endereco_Numero_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:endereco-cidade-cliente"
		xingling="endereco-cidade-cliente">
		Endereco_Cidade_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:endereco-estado-cliente"
		xingling="endereco-estado-cliente">
		Endereco_Estado_Cliente
	</span>
	<span i18n="@@modelo-contrato:cep-cliente" xingling="cep-cliente">
		CEP_Cliente
	</span>
	<span i18n="@@modelo-contrato:profissao-cliente" xingling="profissao-cliente">
		Profissao_Cliente
	</span>
	<span i18n="@@modelo-contrato:mae-cliente" xingling="mae-cliente">
		Mae_Cliente
	</span>
	<span i18n="@@modelo-contrato:pai-cliente" xingling="pai-cliente">
		Pai_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:responsavel-cliente"
		xingling="responsavel-cliente">
		Responsavel_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:responsavel-cliente-cpf"
		xingling="responsavel-cliente-cpf">
		Responsavel_Cliente_Cpf
	</span>
	<span
		i18n="@@modelo-contrato:responsavel-cliente-rg"
		xingling="responsavel-cliente-rg">
		Responsavel_Cliente_Rg
	</span>
	<span
		i18n="@@modelo-contrato:responsavel-cliente-pai"
		xingling="responsavel-cliente-pai">
		Responsavel_Cliente_Pai
	</span>
	<span
		i18n="@@modelo-contrato:responsavel-cliente-pai-cpf"
		xingling="responsavel-cliente-pai-cpf">
		Responsavel_Cliente_Pai_Cpf
	</span>
	<span
		i18n="@@modelo-contrato:responsavel-cliente-pai-rg"
		xingling="responsavel-cliente-pai-rg">
		Responsavel_Cliente_Pai_Rg
	</span>
	<span
		i18n="@@modelo-contrato:responsavel-cliente-mae"
		xingling="responsavel-cliente-mae">
		Responsavel_Cliente_Mae
	</span>
	<span
		i18n="@@modelo-contrato:responsavel-cliente-mae-cpf"
		xingling="responsavel-cliente-mae-cpf">
		Responsavel_Cliente_Mae_Cpf
	</span>
	<span
		i18n="@@modelo-contrato:responsavel-cliente-mae-rg"
		xingling="responsavel-cliente-mae-rg">
		Responsavel_Cliente_Mae_Rg
	</span>
	<span
		xingling="responsavel-financeiro-nome"
		i18n="@@modelo-contrato:responsavel-financeiro-nome">
		Responsavel_Financeiro_Nome_Cliente
	</span>
	<span
		xingling="responsavel-financeiro-cpf"
		i18n="@@modelo-contrato:responsavel-financeiro-cpf">
		Responsavel_Financeiro_Cpf_Cliente
	</span>
	<span
		xingling="responsavel-financeiro-rg"
		i18n="@@modelo-contrato:responsavel-financeiro-rg">
		Responsavel_Financeiro_Rg_Cliente
	</span>
	<span
		xingling="responsavel-financeiro-email"
		i18n="@@modelo-contrato:responsavel-financeiro-email">
		Responsavel_Financeiro_Email_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:complementoend-cliente"
		xingling="complementoend-cliente">
		ComplementoEnd_Cliente
	</span>
	<span i18n="@@modelo-contrato:email-cliente" xingling="email-cliente">
		Email_Cliente
	</span>
	<span i18n="@@modelo-contrato:grupo-cliente" xingling="grupo-cliente">
		Grupo_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:autorizacaocobranca-cliente"
		xingling="autorizacaocobranca-cliente">
		AutorizacaoCobranca_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:observacao-cliente"
		xingling="observacao-cliente">
		Observacao_Cliente
	</span>
	<span i18n="@@modelo-contrato:categoria-cliente" xingling="categoria-cliente">
		Categoria_Cliente
	</span>
	<span i18n="@@modelo-contrato:bairroend-cliente" xingling="bairroend-cliente">
		BairroEnd_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:consultoratual-cliente"
		xingling="consultoratual-cliente">
		ConsultorAtual_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:responsavel-cliente-cpf-nome"
		xingling="responsavel-cliente-cpf-nome">
		Responsavel_Cliente_Cpf_Nome
	</span>
	<span
		i18n="@@modelo-contrato:habilitacao-sesc-cliente"
		xingling="habilitacao-sesc-cliente">
		Habilitacao_Sesc_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:contato-emergencia-cliente"
		xingling="contato-emergencia-cliente">
		Contato_Emergencia_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:telefone-emergencia-cliente"
		xingling="telefone-emergencia-cliente">
		Telefone_Emergencia_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:bandeira-cartao-cliente"
		xingling="bandeira-cartao-cliente">
		Bandeira_Cartao_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:nome-titular-cartao-cliente"
		xingling="nome-titular-cartao-cliente">
		Nome_Titular_Cartao_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:numero-cartao-cliente"
		xingling="numero-cartao-cliente">
		Numero_Cartao_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:avalidade-cartao-cliente"
		xingling="avalidade-cartao-cliente">
		aValidade_Cartao_Cliente
	</span>
	<span
		i18n="@@modelo-contrato:cpf-titular-cartao-cliente"
		xingling="cpf-titular-cartao-cliente">
		CPF_Titular_Cartao_Cliente
	</span>
	<span i18n="@@modelo-contrato:codigo-contrato" xingling="codigo-contrato">
		Codigo_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:responsavel-contrato"
		xingling="responsavel-contrato">
		Responsavel_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:consultorresponsavel-contrato"
		xingling="consultorresponsavel-contrato">
		ConsultorResponsavel_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:vigenciade-contrato"
		xingling="vigenciade-contrato">
		VigenciaDe_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:vigenciaate-contrato"
		xingling="vigenciaate-contrato">
		VigenciaAte_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:vigenciaateajustada-contrato"
		xingling="vigenciaateajustada-contrato">
		VigenciaAteAjustada_Contrato
	</span>
	<span i18n="@@modelo-contrato:duracao-contrato" xingling="duracao-contrato">
		Duracao_Contrato
	</span>
	<span i18n="@@modelo-contrato:horario-contrato" xingling="horario-contrato">
		Horario_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valorbasecalculo-contrato"
		xingling="valorbasecalculo-contrato">
		ValorBaseCalculo_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valorfinal-contrato"
		xingling="valorfinal-contrato">
		ValorFinal_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:observacao-contrato"
		xingling="observacao-contrato">
		Observacao_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:nomemodalidades-contrato"
		xingling="nomemodalidades-contrato">
		NomeModalidades_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:nomecompletomodalidades-contrato"
		xingling="nomecompletomodalidades-contrato">
		NomeCompletoModalidades_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:nrvezesnomecompletomodalidades-contrato"
		xingling="nrvezesnomecompletomodalidades-contrato">
		NrVezesNomeCompletoModalidades_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valormensal-contrato"
		xingling="valormensal-contrato">
		valorMensal_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valormensaldesconto-contrato"
		xingling="valormensaldesconto-contrato">
		valorMensalDesconto_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valormensaladequado-contrato"
		xingling="valormensaladequado-contrato">
		valorMensalAdequado_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valormensalbase-contrato"
		xingling="valormensalbase-contrato">
		ValorMensalBase_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valordescontoextra-contrato"
		xingling="valordescontoextra-contrato">
		valorDescontoExtra_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:dtlancamento-contrato"
		xingling="dtlancamento-contrato">
		DtLancamento_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valorporextenso-contrato"
		xingling="valorporextenso-contrato">
		ValorPorExtenso_Contrato
	</span>
	<span i18n="@@modelo-contrato:convenio-contrato" xingling="convenio-contrato">
		Convenio_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valormatricula-contrato"
		xingling="valormatricula-contrato">
		ValorMatricula_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valorrematricula-contrato"
		xingling="valorrematricula-contrato">
		ValorRematricula_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:condicaopagamento-contrato"
		xingling="condicaopagamento-contrato">
		CondicaoPagamento_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:nomeproduto-contrato"
		xingling="nomeproduto-contrato">
		NomeProduto_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:qtdproduto-contrato"
		xingling="qtdproduto-contrato">
		QtdProduto_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valorproduto-contrato"
		xingling="valorproduto-contrato">
		ValorProduto_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valordescontoproduto-contrato"
		xingling="valordescontoproduto-contrato">
		ValorDescontoProduto_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valorunitarioproduto-contrato"
		xingling="valorunitarioproduto-contrato">
		ValorUnitarioProduto_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:tabelaprodutos-contrato"
		xingling="tabelaprodutos-contrato">
		TabelaProdutos_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:conveniodescontoresumo-contrato"
		xingling="conveniodescontoresumo-contrato">
		ConvenioDescontoResumo_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:diavencimentocartao-contratorecorrencia"
		xingling="diavencimentocartao-contratorecorrencia">
		diaVencimentoCartao_ContratoRecorrencia
	</span>
	<span
		i18n="@@modelo-contrato:valoranuidade-contratorecorrencia"
		xingling="valoranuidade-contratorecorrencia">
		valorAnuidade_ContratoRecorrencia
	</span>
	<span
		i18n="@@modelo-contrato:valortotalsemdesconto-contrato"
		xingling="valortotalsemdesconto-contrato">
		valorTotalSemDesconto_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valortotaldescontocontrato-contrato"
		xingling="valortotaldescontocontrato-contrato">
		valorTotalDescontoContrato_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valortotalcontratosemdescontoextra-contrato"
		xingling="valortotalcontratosemdescontoextra-contrato">
		valorTotalContratoSemDescontoExtra_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valorparcelamensal-contrato"
		xingling="valorparcelamensal-contrato">
		valorParcelaMensal_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:quantidadecreditotreino-contrato"
		xingling="quantidadecreditotreino-contrato">
		quantidadeCreditoTreino_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:totaldias-contrato"
		xingling="totaldias-contrato">
		totalDias_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:diascarencia-contrato"
		xingling="diascarencia-contrato">
		DiasCarencia_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:nomemodalidadesnrvezes-contrato"
		xingling="nomemodalidadesnrvezes-contrato">
		NomeModalidadesNrVezes_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valoradesao-contrato"
		xingling="valoradesao-contrato">
		ValorAdesao_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:saldo-credito-contrato"
		xingling="saldo-credito-contrato">
		Saldo_Credito_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valorcheiomatricula-contrato"
		xingling="valorcheiomatricula-contrato">
		ValorCheioMatricula_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valordescontomatricula-contrato"
		xingling="valordescontomatricula-contrato">
		ValorDescontoMatricula_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valorcheiorematricula-contrato"
		xingling="valorcheiorematricula-contrato">
		ValorCheioRematricula_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valordescontorematricula-contrato"
		xingling="valordescontorematricula-contrato">
		ValorDescontoRematricula_Contrato
	</span>
	<span
		i18n="@@modelo-contrato:valordescontoanuidade-contratorecorrencia"
		xingling="valordescontoanuidade-contratorecorrencia">
		valorDescontoAnuidade_ContratoRecorrencia
	</span>
	<span
		i18n="@@modelo-contrato:valorfinalanuidade-contratorecorrencia"
		xingling="valorfinalanuidade-contratorecorrencia">
		valorFinalAnuidade_ContratoRecorrencia
	</span>
	<span
		xingling="docrg-assinatura-contrato"
		i18n="@@modelo-contrato:docRg_Assinatura_Contrato">
		docRg_Assinatura_Contrato
	</span>
	<span
		xingling="endereco-assinatura-contrato"
		i18n="@@modelo-contrato:endereco_Assinatura_Contrato">
		endereco_Assinatura_Contrato
	</span>
	<span
		xingling="atestado-assinatura-contrato"
		i18n="@@modelo-contrato:atestado_Assinatura_Contrato">
		atestado_Assinatura_Contrato
	</span>
	<span
		xingling="anexo1-assinatura-contrato"
		i18n="@@modelo-contrato:anexo1_Assinatura_Contrato">
		anexo1_Assinatura_Contrato
	</span>
	<span
		xingling="anexo2-assinatura-contrato"
		i18n="@@modelo-contrato:anexo2_Assinatura_Contrato">
		anexo2_Assinatura_Contrato
	</span>
	<span i18n="@@modelo-contrato:codigo-plano" xingling="codigo-plano">
		Codigo_Plano
	</span>
	<span i18n="@@modelo-contrato:descricao-plano" xingling="descricao-plano">
		Descricao_Plano
	</span>
	<span i18n="@@modelo-contrato:vigenciade-plano" xingling="vigenciade-plano">
		VigenciaDe_Plano
	</span>
	<span i18n="@@modelo-contrato:vigenciaate-plano" xingling="vigenciaate-plano">
		VigenciaAte_Plano
	</span>
	<span i18n="@@modelo-contrato:ingressoate-plano" xingling="ingressoate-plano">
		IngressoAte_Plano
	</span>
	<span i18n="@@modelo-contrato:codigo-modalidade" xingling="codigo-modalidade">
		Codigo_Modalidade
	</span>
	<span
		i18n="@@modelo-contrato:valormensal-modalidade"
		xingling="valormensal-modalidade">
		ValorMensal_Modalidade
	</span>
	<span
		i18n="@@modelo-contrato:nrvezes-modalidade"
		xingling="nrvezes-modalidade">
		NrVezes_Modalidade
	</span>
	<span i18n="@@modelo-contrato:nome-modalidade" xingling="nome-modalidade">
		Nome_Modalidade
	</span>
	<span
		i18n="@@modelo-contrato:nomevezes-modalidade"
		xingling="nomevezes-modalidade">
		NomeVezes_Modalidade
	</span>
	<span i18n="@@modelo-contrato:codigo-da-turma" xingling="codigo-da-turma">
		Codigo da Turma
	</span>
	<span
		i18n="@@modelo-contrato:horario-da-turma-completo"
		xingling="horario-da-turma-completo">
		Horário da turma (Completo)
	</span>
	<span
		i18n="@@modelo-contrato:identificador-da-turma"
		xingling="identificador-da-turma">
		Identificador da Turma
	</span>
	<span
		i18n="@@modelo-contrato:data-inicial-da-vigencia-da-turma"
		xingling="data-inicial-da-vigencia-da-turma">
		Data Inicial da Vigência da Turma
	</span>
	<span
		i18n="@@modelo-contrato:data-final-da-vigencia-da-turma"
		xingling="data-final-da-vigencia-da-turma">
		Data Final da Vigência da Turma
	</span>
	<span
		i18n="@@modelo-contrato:idade-minima-da-turma"
		xingling="idade-minima-da-turma">
		Idade Mínima da Turma
	</span>
	<span
		i18n="@@modelo-contrato:idade-maxima-da-turma"
		xingling="idade-maxima-da-turma">
		Idade Máxima da Turma
	</span>
	<span
		i18n="@@modelo-contrato:horario-da-turma-resumida"
		xingling="horario-da-turma-resumida">
		Horário da turma (Resumida)
	</span>
	<span i18n="@@modelo-contrato:codigo-movparcela" xingling="codigo-movparcela">
		Codigo_MovParcela
	</span>
	<span
		i18n="@@modelo-contrato:codigo-movparcela-sem-renegociadas"
		xingling="codigo-movparcela-sem-renegociadas">
		Codigo_MovParcela_Sem_Renegociadas
	</span>
	<span
		i18n="@@modelo-contrato:datavencimento-movparcela"
		xingling="datavencimento-movparcela">
		DataVencimento_MovParcela
	</span>
	<span
		i18n="@@modelo-contrato:datavencimento-movparcela-sem-renegociadas"
		xingling="datavencimento-movparcela-sem-renegociadas">
		DataVencimento_MovParcela_Sem_Renegociadas
	</span>
	<span
		i18n="@@modelo-contrato:valorparcela-movparcela"
		xingling="valorparcela-movparcela">
		ValorParcela_MovParcela
	</span>
	<span
		i18n="@@modelo-contrato:valorparcela-movparcela-sem-renegociadas"
		xingling="valorparcela-movparcela-sem-renegociadas">
		ValorParcela_MovParcela_Sem_Renegociadas
	</span>
	<span
		i18n="@@modelo-contrato:percentualmulta-movparcela"
		xingling="percentualmulta-movparcela">
		PercentualMulta_MovParcela
	</span>
	<span
		i18n="@@modelo-contrato:percentualjuro-movparcela"
		xingling="percentualjuro-movparcela">
		PercentualJuro_MovParcela
	</span>
	<span
		i18n="@@modelo-contrato:descricao-movparcela"
		xingling="descricao-movparcela">
		Descricao_MovParcela
	</span>
	<span
		i18n="@@modelo-contrato:descricao-movparcela-sem-renegociadas"
		xingling="descricao-movparcela-sem-renegociadas">
		Descricao_MovParcela_Sem_Renegociadas
	</span>
	<span i18n="@@modelo-contrato:codigo-recibo" xingling="codigo-recibo">
		codigo_Recibo
	</span>
	<span i18n="@@modelo-contrato:valortotal-recibo" xingling="valortotal-recibo">
		valorTotal_Recibo
	</span>
	<span
		i18n="@@modelo-contrato:nomepessoapagador-recibo"
		xingling="nomepessoapagador-recibo">
		nomePessoaPagador_Recibo
	</span>
	<span
		i18n="@@modelo-contrato:responsavellancamento-recibo"
		xingling="responsavellancamento-recibo">
		responsavelLancamento_Recibo
	</span>
	<span i18n="@@modelo-contrato:contrato-recibo" xingling="contrato-recibo">
		contrato_Recibo
	</span>
	<span i18n="@@modelo-contrato:data-recibo" xingling="data-recibo">
		data_Recibo
	</span>
	<span
		i18n="@@modelo-contrato:valorporextenso-recibo"
		xingling="valorporextenso-recibo">
		valorPorExtenso_Recibo
	</span>
	<span
		i18n="@@modelo-contrato:dataimpressao-recibo"
		xingling="dataimpressao-recibo">
		dataImpressao_Recibo
	</span>
	<span
		i18n="@@modelo-contrato:tipoformapagamento-movpagamento"
		xingling="tipoformapagamento-movpagamento">
		tipoFormaPagamento_MovPagamento
	</span>
	<span
		i18n="@@modelo-contrato:operadoracc-movpagamento"
		xingling="operadoracc-movpagamento">
		operadoraCC_MovPagamento
	</span>
	<span
		i18n="@@modelo-contrato:valor-movpagamento"
		xingling="valor-movpagamento">
		valor_MovPagamento
	</span>
	<span
		i18n="@@modelo-contrato:parcelascc-movpagamento"
		xingling="parcelascc-movpagamento">
		parcelasCC_MovPagamento
	</span>
	<span i18n="@@modelo-contrato:valor-cheque" xingling="valor-cheque">
		valor_Cheque
	</span>
	<span i18n="@@modelo-contrato:banco-cheque" xingling="banco-cheque">
		banco_Cheque
	</span>
	<span i18n="@@modelo-contrato:agencia-cheque" xingling="agencia-cheque">
		agencia_Cheque
	</span>
	<span
		i18n="@@modelo-contrato:contacorrente-cheque"
		xingling="contacorrente-cheque">
		contaCorrente_Cheque
	</span>
	<span i18n="@@modelo-contrato:numero-cheque" xingling="numero-cheque">
		numero_Cheque
	</span>
	<span
		i18n="@@modelo-contrato:datacompensacao-cheque"
		xingling="datacompensacao-cheque">
		dataCompensacao_Cheque
	</span>
	<span i18n="@@modelo-contrato:codigo-composicao" xingling="codigo-composicao">
		Codigo_Composicao
	</span>
	<span
		i18n="@@modelo-contrato:descricao-composicao"
		xingling="descricao-composicao">
		Descricao_Composicao
	</span>
	<span
		i18n="@@modelo-contrato:precocomposicao-composicao"
		xingling="precocomposicao-composicao">
		PrecoComposicao_Composicao
	</span>
	<span i18n="@@modelo-contrato:codigo-usuario" xingling="codigo-usuario">
		Codigo_Usuario
	</span>
	<span i18n="@@modelo-contrato:nome-usuario" xingling="nome-usuario">
		Nome_Usuario
	</span>
	<span i18n="@@modelo-contrato:username-usuario" xingling="username-usuario">
		Username_Usuario
	</span>
	<span
		i18n="@@modelo-contrato:usuario-assinatura"
		xingling="usuario-assinatura">
		Usuario_Assinatura
	</span>
	<span i18n="@@modelo-contrato:codigo-empresa" xingling="codigo-empresa">
		Codigo_Empresa
	</span>
	<span
		i18n="@@modelo-contrato:assinatura-empresa"
		xingling="assinatura-empresa">
		Assinatura_Empresa
	</span>
	<span i18n="@@modelo-contrato:fax-empresa" xingling="fax-empresa">
		Fax_Empresa
	</span>
	<span i18n="@@modelo-contrato:site-empresa" xingling="site-empresa">
		Site_Empresa
	</span>
	<span i18n="@@modelo-contrato:email-empresa" xingling="email-empresa">
		Email_Empresa
	</span>
	<span
		i18n="@@modelo-contrato:telcomercial1-empresa"
		xingling="telcomercial1-empresa">
		TelComercial1_Empresa
	</span>
	<span
		i18n="@@modelo-contrato:telcomercial2-empresa"
		xingling="telcomercial2-empresa">
		TelComercial2_Empresa
	</span>
	<span
		i18n="@@modelo-contrato:telcomercial3-empresa"
		xingling="telcomercial3-empresa">
		TelComercial3_Empresa
	</span>
	<span
		i18n="@@modelo-contrato:inscestadual-empresa"
		xingling="inscestadual-empresa">
		InscEstadual_Empresa
	</span>
	<span i18n="@@modelo-contrato:cnpj-empresa" xingling="cnpj-empresa">
		Cnpj_Empresa
	</span>
	<span i18n="@@modelo-contrato:cep-empresa" xingling="cep-empresa">
		Cep_Empresa
	</span>
	<span
		i18n="@@modelo-contrato:complemento-empresa"
		xingling="complemento-empresa">
		Complemento_Empresa
	</span>
	<span i18n="@@modelo-contrato:numero-empresa" xingling="numero-empresa">
		Numero_Empresa
	</span>
	<span i18n="@@modelo-contrato:setor-empresa" xingling="setor-empresa">
		Setor_Empresa
	</span>
	<span i18n="@@modelo-contrato:endereco-empresa" xingling="endereco-empresa">
		Endereco_Empresa
	</span>
	<span
		i18n="@@modelo-contrato:razaosocial-empresa"
		xingling="razaosocial-empresa">
		RazaoSocial_Empresa
	</span>
	<span i18n="@@modelo-contrato:nome-empresa" xingling="nome-empresa">
		Nome_Empresa
	</span>
	<span i18n="@@modelo-contrato:cidade-empresa" xingling="cidade-empresa">
		Cidade_Empresa
	</span>
	<span i18n="@@modelo-contrato:estado-empresa" xingling="estado-empresa">
		Estado_Empresa
	</span>
	<span i18n="@@modelo-contrato:data-venda" xingling="data-venda">
		Data_Venda
	</span>
	<span i18n="@@modelo-contrato:valortotal-venda" xingling="valortotal-venda">
		ValorTotal_Venda
	</span>
	<span i18n="@@modelo-contrato:valorfinal-venda" xingling="valorfinal-venda">
		ValorFinal_Venda
	</span>
	<span
		i18n="@@modelo-contrato:valordesconto-venda"
		xingling="valordesconto-venda">
		ValorDesconto_Venda
	</span>
	<span i18n="@@modelo-contrato:codigo-venda" xingling="codigo-venda">
		Codigo_Venda
	</span>
	<span i18n="@@modelo-contrato:codigo-itens" xingling="codigo-itens">
		Codigo_Itens
	</span>
	<span i18n="@@modelo-contrato:descricao-itens" xingling="descricao-itens">
		Descricao_Itens
	</span>
	<span
		i18n="@@modelo-contrato:valorunitario-item-venda"
		xingling="valorunitario-item-venda">
		ValorUnitario_Item_Venda
	</span>
	<span
		i18n="@@modelo-contrato:valordesconto-item-venda"
		xingling="valordesconto-item-venda">
		ValorDesconto_Item_Venda
	</span>
	<span
		i18n="@@modelo-contrato:valorfinal-item-venda"
		xingling="valorfinal-item-venda">
		ValorFinal_Item_Venda
	</span>
	<span i18n="@@modelo-contrato:quantidade-itens" xingling="quantidade-itens">
		Quantidade_Itens
	</span>
	<span i18n="@@modelo-contrato:codigo-pacoteitem" xingling="codigo-pacoteitem">
		Codigo_PacoteItem
	</span>
	<span
		i18n="@@modelo-contrato:descricao-pacoteitem"
		xingling="descricao-pacoteitem">
		Descricao_PacoteItem
	</span>
	<span i18n="@@modelo-contrato:valor-pacoteitem" xingling="valor-pacoteitem">
		Valor_PacoteItem
	</span>

	<span i18n="@@modelo-contrato:copiado-sucesso" xingling="copiado-sucesso">
		Tag copiada com sucesso
	</span>
</pacto-traducoes-xingling>
