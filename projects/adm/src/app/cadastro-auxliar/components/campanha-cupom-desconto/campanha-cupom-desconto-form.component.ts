import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import {
	CampanhaCupomDesconto,
	ZwBootCampanhaCupomDescontoService,
} from "adm-legado-api";
import { PerfilAcessoRecurso, SessionService } from "sdk";
import { AdmRestService } from "@adm/adm-rest.service";
import { ModalService } from "@base-core/modal/modal.service";
import { PactoModalSize } from "ui-kit";
import { ModalNovoLoteCupomComponent } from "@adm/cadastro-auxliar/components/campanha-cupom-desconto/modal-novo-lote-cupom/modal-novo-lote-cupom.component";
import { PerfilAcessoRecursoNome } from "@adm/perfil-acesso/perfil-acesso-recurso.model";

@Component({
	selector: "adm-campanha-cupom-desconto-form",
	templateUrl: "./campanha-cupom-desconto-form.component.html",
	styleUrls: ["./campanha-cupom-desconto-form.component.scss"],
})
export class CampanhaCupomDescontoFormComponent implements OnInit {
	form: FormGroup;
	campanhaCupomDesconto: any;
	id: string;
	planos = [];
	produtos = [
		{ codigo: null, nome: "-" },
		{ codigo: "ADESÃO PLANO RECORRENTE", nome: "ADESÃO PLANO RECORRENTE" },
		{ codigo: "ANUIDADE PLANO RECORRENTE", nome: "ANUIDADE PLANO RECORRENTE" },
	];
	parcelas = [];
	restricoes = [];
	premios = [];
	cupons = [];
	isEdicao = false;
	textoAcaoInforme = "cadastrar";
	urlLog: string;
	tipoPremioOptions = [
		{ id: null, nome: "-" },
		{ id: "PRODUTO", nome: "Produto" },
		{ id: "MENSALIDADE", nome: "Mensalidade" },
	];
	tipoDescontoOptions = [
		{ id: null, nome: "-" },
		{ id: "PE", nome: "Percentual" },
		{ id: "VA", nome: "Valor" },
	];
	recurso: PerfilAcessoRecurso;

	constructor(
		private fb: FormBuilder,
		private router: Router,
		private pactoModal: ModalService,
		private admRest: AdmRestService,
		private activatedRoute: ActivatedRoute,
		private sessionService: SessionService,
		private notificationService: SnotifyService,
		private zwBootCampanhaCupomDescontoService: ZwBootCampanhaCupomDescontoService,
		private cd: ChangeDetectorRef
	) {
		this.recurso = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.CAMPANHA_CUPOM_DESCONTO
		);
	}

	ngOnInit(): void {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.isEdicao = !!this.id && this.id !== "novo";
		this.textoAcaoInforme = this.isEdicao ? "editar" : "cadastrar";

		this.form = this.fb.group({
			id: [{ value: "", disabled: true }],
			descricaoCampanha: ["", [Validators.required]],
			vigenciaInicial: [null, [Validators.required]],
			vigenciaFinal: [null, [Validators.required]],
			totalLote: [{ value: 0, disabled: true }],
			quantidadeCupomExtra: [{ value: 0, disabled: true }],
			totalCupomUtilizado: [{ value: 0, disabled: true }],
			planoRestricaoOpt: [null],
			tipoPremioOpt: [null],
			parcelaOpt: [null],
			produtoOpt: [null],
			planoOpt: [null],
			tipoDescontoOpt: [null],
			percentualDesconto: [0.0],
			valorDesconto: [0.0],
		});

		if (
			this.isEdicao &&
			(this.recurso.consultar || this.recurso.incluirConsultar) &&
			!this.recurso.editar &&
			!this.recurso.incluir
		) {
			this.form.disable();
		}

		this.carregarPlanos();
		this.carregarParcelas();

		if (this.isEdicao) {
			this.carregarCampanhaCupomDesconto();
			this.urlLog = this.admRest.buildFullUrlCadAux(
				`log/CAMPANHACUPOMDESCONTO/${this.id}`
			);
		}
	}

	carregarPlanos() {
		this.zwBootCampanhaCupomDescontoService.planos().subscribe(
			(response) => {
				this.planos = response.content || [];
				this.cd.detectChanges();
			},
			(error) => {
				this.notificationService.error("Erro ao carregar planos");
			}
		);
	}

	carregarParcelas() {
		this.zwBootCampanhaCupomDescontoService.parcelas().subscribe(
			(response) => {
				this.parcelas = response.content || [];
				this.cd.detectChanges();
			},
			(error) => {
				this.notificationService.error("Erro ao carregar parcelas");
			}
		);
	}

	carregarCampanhaCupomDesconto() {
		this.zwBootCampanhaCupomDescontoService.find(this.id).subscribe(
			(response) => {
				this.campanhaCupomDesconto = response.content;
				this.form.patchValue(this.campanhaCupomDesconto);
				if (this.campanhaCupomDesconto.listaPlanoRestricao) {
					this.restricoes = this.campanhaCupomDesconto.listaPlanoRestricao;
				}
				if (this.campanhaCupomDesconto.listaPremioPortador) {
					this.premios = this.campanhaCupomDesconto.listaPremioPortador;
				}
				if (this.campanhaCupomDesconto.listaCupom) {
					this.cupons = this.campanhaCupomDesconto.listaCupom;
				}
				if (this.campanhaCupomDesconto.vigenciaInicial) {
					this.form
						.get("vigenciaInicial")
						.setValue(new Date(this.campanhaCupomDesconto.vigenciaInicial));
				}
				if (this.campanhaCupomDesconto.vigenciaFinal) {
					this.form
						.get("vigenciaFinal")
						.setValue(new Date(this.campanhaCupomDesconto.vigenciaFinal));
				}
				this.cd.detectChanges();
			},
			(error) => {
				this.notificationService.error(
					"Erro ao carregar campanha cupom de desconto"
				);
				this.voltarListagem();
			}
		);
	}

	voltarListagem() {
		this.router.navigate(["adm", "cad-aux", "campanha-cupom-desconto"]);
	}

	salvar(): void {
		if (
			!this.recurso ||
			!(
				this.recurso.editar ||
				this.recurso.incluir ||
				!(
					this.isEdicao &&
					(this.recurso.consultar || this.recurso.incluirConsultar)
				)
			)
		) {
			let resource = "INCLUIR";
			if (this.isEdicao) {
				resource = "EDITAR";
			}
			this.notificationService.error(
				`Você não possui permissão para esta operação, "${resource} 5.62 - CAMPANHA CUPOM DESCONTO"`,
				{
					timeout: 5000,
					bodyMaxLength: 300,
				}
			);
			return;
		}

		if (this.form.invalid) {
			this.notificationService.error("Preencha todos os campos obrigatórios.");
			return;
		}

		const dadosFormulario = this.form.getRawValue();

		// Extrai apenas o código do plano se for objeto
		// if (dadosFormulario.descricaoPlano && typeof dadosFormulario.descricaoPlano === "object") {
		// 	dadosFormulario.descricaoPlano = dadosFormulario.descricaoPlano.codigo;
		// }

		// Object.assign(this.campanhaCupomDesconto, dadosFormulario);

		dadosFormulario.listaPlanoRestricao = this.restricoes;
		dadosFormulario.listaPremioPortador = this.premios;
		dadosFormulario.listaCupom = this.cupons;

		this.zwBootCampanhaCupomDescontoService.save(dadosFormulario).subscribe(
			(response) => {
				this.notificationService.success(
					this.isEdicao
						? "Campanha Cupom de Desconto atualizada com sucesso!"
						: "Campanha Cupom de Desconto cadastrada com sucesso!"
				);
				this.voltarListagem();
			},
			(error) => {
				const errorMessage =
					error &&
					error.error &&
					error.error.meta &&
					error.error.meta.messageValue
						? error.error.meta.messageValue
						: "Erro ao salvar campanha cupom de desconto.";
				this.notificationService.error(errorMessage);
			}
		);
	}

	novo() {
		this.form.reset();
		this.campanhaCupomDesconto = new CampanhaCupomDesconto();
		this.isEdicao = false;
		this.restricoes = [];
		this.cd.detectChanges();
	}

	excluir() {
		if (!this.id) {
			this.notificationService.warning(
				"Nenhum registro selecionado para exclusão."
			);
			return;
		}

		if (
			confirm("Tem certeza que deseja excluir esta campanha cupom de desconto?")
		) {
			this.zwBootCampanhaCupomDescontoService.delete(Number(this.id)).subscribe(
				(response) => {
					this.notificationService.success(
						"Campanha Cupom de Desconto excluída com sucesso!"
					);
					this.voltarListagem();
				},
				(error) => {
					const errorMessage =
						error &&
						error.error &&
						error.error.meta &&
						error.error.meta.messageValue
							? error.error.meta.messageValue
							: "Erro ao excluir campanha cupom de desconto.";
					this.notificationService.error(errorMessage);
				}
			);
		}
	}

	adicionarRestricao() {
		const planoSelecionado = this.form.get("planoRestricaoOpt");
		if (planoSelecionado && planoSelecionado.value) {
			const plano = this.planos.find(
				(p) => p.codigo === planoSelecionado.value
			);
			if (plano) {
				const restricaoExistente = this.restricoes.find(
					(r) => r === plano.descricao
				);
				if (!restricaoExistente) {
					this.restricoes.push(plano.descricao);
					this.notificationService.success("Restrição adicionada com sucesso!");
					this.form.get("planoRestricaoOpt").setValue(null);
				} else {
					this.notificationService.warning("Esta restrição já foi adicionada!");
				}
			}
		} else {
			this.notificationService.warning(
				"Selecione um plano para adicionar como restrição."
			);
		}
	}

	removerRestricao(index: number) {
		if (confirm("Tem certeza que deseja remover esta restrição?")) {
			this.restricoes.splice(index, 1);
			this.notificationService.success("Restrição removida com sucesso!");
		}
	}

	cancelar() {
		this.voltarListagem();
	}

	get tituloFormulario(): string {
		return this.isEdicao
			? "Editar Campanha Cupom de Desconto"
			: "Nova Campanha Cupom de Desconto";
	}

	adicionarPremio() {
		const tipoPremioSelecionado = this.form.get("tipoPremioOpt");
		const parcelaSelecionado = this.form.get("parcelaOpt");
		const produtoSelecionado = this.form.get("produtoOpt");
		const tipoDescontoSelecionado = this.form.get("tipoDescontoOpt");
		const percentualDesconto = this.form.get("percentualDesconto");
		const valorDesconto = this.form.get("valorDesconto");

		const premio = {
			tipoPremio: null,
			descricaoPremio: null,
			descricaoPlano: null,
			valorDesconto: 0,
			percentualDesconto: 0,
		};

		if (tipoPremioSelecionado && tipoPremioSelecionado.value) {
			premio.tipoPremio = tipoPremioSelecionado.value;
		} else {
			this.notificationService.warning("Selecione um tipo de prêmio.");
			return;
		}

		if (tipoPremioSelecionado.value === "PRODUTO") {
			if (produtoSelecionado && produtoSelecionado.value) {
				premio.descricaoPremio = produtoSelecionado.value;
			} else {
				this.notificationService.warning("Selecione a descrição do produto.");
				return;
			}
		} else if (tipoPremioSelecionado.value === "MENSALIDADE") {
			if (parcelaSelecionado && parcelaSelecionado.value) {
				const parcel = this.parcelas.find(
					(p) => p.codigo === parcelaSelecionado.value
				);
				premio.descricaoPremio = parcel.descricao;
			} else {
				this.notificationService.warning("Selecione a descrição da parcela.");
				return;
			}
		}

		if (tipoDescontoSelecionado && tipoDescontoSelecionado.value) {
			if (tipoDescontoSelecionado.value === "PE") {
				if (percentualDesconto && percentualDesconto.value) {
					premio.valorDesconto = 0;
					premio.percentualDesconto = percentualDesconto.value;
				} else {
					this.notificationService.warning("Informe o percentual de desconto.");
					return;
				}
			} else if (tipoDescontoSelecionado.value === "VA") {
				if (valorDesconto && valorDesconto.value) {
					premio.valorDesconto = valorDesconto.value;
					premio.percentualDesconto = 0;
				} else {
					this.notificationService.warning("Informe o valor de desconto.");
					return;
				}
			}
		} else {
			this.notificationService.warning("Selecione um tipo de desconto.");
			return;
		}

		const planoSelecionado = this.form.get("planoOpt");
		if (planoSelecionado && planoSelecionado.value) {
			const plano = this.planos.find(
				(p) => p.codigo === planoSelecionado.value
			);
			if (plano) {
				premio.descricaoPlano = plano.descricao;
			}
		}

		const premioExiste = this.premios.find(
			(r) =>
				r.tipoPremio === premio.tipoPremio &&
				r.descricaoPremio === premio.descricaoPremio &&
				r.descricaoPlano === premio.descricaoPlano &&
				r.valorDesconto === premio.valorDesconto &&
				r.percentualDesconto === premio.percentualDesconto
		);
		if (!premioExiste) {
			this.premios.push(premio);
			this.notificationService.success("Prêmio adicionado com sucesso!");
		} else {
			this.notificationService.warning("Esta prêmio já foi adicionado!");
		}
	}

	removerPremio(index: number) {
		if (confirm("Tem certeza que deseja remover este prêmio?")) {
			this.premios.splice(index, 1);
			this.notificationService.success("Prêmio removido com sucesso!");
		}
	}

	adicionarNovoLoteCupom() {
		const dialogRef = this.pactoModal.open(
			"Novo lote de cupom de desconto",
			ModalNovoLoteCupomComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.campanha = this.campanhaCupomDesconto;
		dialogRef.componentInstance.update.subscribe((res) => {
			if (res === "update") {
				this.carregarCampanhaCupomDesconto();
				this.cd.detectChanges();
			}
		});
	}
}
