<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@conta-corrente:modulo"
	i18n-pageTitle="@@conta-corrente:title"
	modulo="Administrativo"
	pageTitle="Conta corrente">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableDataComponent
			(btnAddClick)="novaContaCorrente()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editContaCorrente($event)"
			[actionTitulo]="traducao.getLabel('table-column-acoes')"
			[enableDs3]="true"
			[filterConfig]="filterConfig"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="tableData"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="contaCorrente"></pacto-relatorio>
	</div>
</adm-layout>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@mensagem-sem-permissao" xingling="mensagem-sem-permissao">
		Seu usuário não possui permissão, procure seu administrador
	</span>
	<span i18n="@@conta-corrente:success-excluded" xingling="success-excluded">
		Conta corrente excluída com sucesso
	</span>
	<span i18n="@@adm:action-editar" xingling="action-edit">Editar</span>
	<span i18n="@@adm:tooltip-editar-conta-corrente" xingling="tooltip-editar">
		Editar uma conta corrente
	</span>
	<span i18n="@@adm:action-excluir" xingling="action-delete">Excluir</span>
	<span i18n="@@adm:tooltip-excluir-conta-corrente" xingling="tooltip-excluir">
		Excluir uma conta corrente
	</span>
	<span i18n="@@acoes:table-column-acoes" xingling="table-column-acoes">
		Ações
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@conta-corrente:column-codigo">Cód</span>
</ng-template>
<ng-template #columnAgencia>
	<span i18n="@@conta-corrente:column-agencia">Agência</span>
</ng-template>
<ng-template #columnAgenciaDv>
	<span i18n="@@conta-corrente:column-agenciadv">Dígito agência</span>
</ng-template>
<ng-template #columnContaCorrente>
	<span i18n="@@conta-corrente:column-conta-corrente">Conta corrente</span>
</ng-template>
<ng-template #columnContaCorrenteDv>
	<span i18n="@@conta-corrente:column-conta-correntedv">
		Dígito conta corrente
	</span>
</ng-template>
<ng-template #columnNomeBanco>
	<span i18n="@@conta-corrente:column-nome-banco">Nome do banco</span>
</ng-template>
