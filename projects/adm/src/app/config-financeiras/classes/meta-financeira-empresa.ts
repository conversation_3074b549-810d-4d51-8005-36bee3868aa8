export class MetaFinanceiraEmpresa {
	codigo: number;
	empresa: any;
	mes: number;
	ano: number;
	anoMes: string;
	descricao: string;
	valores: Array<MetaFinanceiraEmpresaValores>;
	consultores: Array<MetaFinanceiraConsultor>;
	meta1: number;
	meta2: number;
	meta3: number;
	meta4: number;
	meta5: number;
}

export class MetaFinanceiraEmpresaValores {
	codigo: number;
	valor: number;
	cor: string;
	observacao: string;
	meta: string;
}

export class MetaFinanceiraConsultor {
	codigo: number;
	colaborador: Colaborador;
	percentagem: number;
	meta1 = 0.0;
	meta2 = 0.0;
	meta3 = 0.0;
	meta4 = 0.0;
	meta5 = 0.0;
}

export class Colaborador {
	codigo: number;
	pessoa: any;
}
