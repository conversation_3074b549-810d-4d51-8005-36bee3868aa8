<adm-config-normal
	[plano]="plano"
	*ngIf="this.isPlanoNormalOuAvancado()"
	[form]="formNormal"
	[mostrarAbaFerias]="mostrarAbaFerias"></adm-config-normal>
<adm-config-recorrencia
	[plano]="plano"
	*ngIf="
		(plano.tipoPlano === 'PLANO_RECORRENCIA' ||
			plano.tipoPlano === 'PLANO_PERSONAL' ||
			plano.tipoPlano === 'PLANO_CREDITO') &&
		!configRecorrenciaDadosContratuais
	"
	[form]="formRecorrenciaPersonal"></adm-config-recorrencia>
<adm-config-recorrencia-dados-contratuais
	*ngIf="
		(plano.tipoPlano === 'PLANO_RECORRENCIA' ||
			plano.tipoPlano === 'PLANO_PERSONAL' ||
			plano.tipoPlano === 'PLANO_CREDITO') &&
		configRecorrenciaDadosContratuais
	"
	[form]="formRecorrenciaDadosContratuais"
	[plano]="plano"></adm-config-recorrencia-dados-contratuais>

<div class="action-container btn-row-adm">
	<pacto-cat-button
		id="btn-save-config"
		i18n-label="@@adm:btn-salvar-config"
		label="Salvar Configurações"
		[type]="buttonType.PRIMARY"
		(click)="salvar()"></pacto-cat-button>
</div>
