import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoService } from "../../cadastrar-plano/plano.service";
import { AdmRestService } from "../../../../adm-rest.service";
import { SnotifyService } from "ng-snotify";
import { ActivatedRoute, Router } from "@angular/router";
import { Plano, TipoPlano } from "../../../plano.model";
import { PpDadosContratuaisComponent } from "../pp-dados-contratuais/pp-dados-contratuais.component";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "adm-pp-cadastro",
	templateUrl: "./pp-cadastro.component.html",
	styleUrls: ["./pp-cadastro.component.scss", "../../../css/plano.scss"],
})
export class PpCadastroComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("dadosContratuaisComponent", { static: false })
	dadosContratuaisComponent: PpDadosContratuaisComponent;
	loading = false;
	plano: Plano = new Plano();
	isSaving = false;
	planoNome = "";
	constructor(
		private planoCommonsService: PlanoCommonsService,
		public planoStateService: PlanoStateService,
		private planoService: PlanoService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private activatedRoute: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private router: Router
	) {}

	ngOnInit() {
		const clonar = this.activatedRoute.snapshot.queryParamMap.get("clonar");
		if (clonar) {
			this.loading = true;
			this.planoCommonsService
				.clonar(this.activatedRoute, this.planoService)
				.subscribe((response) => {
					this.plano = response.content;
					this.plano = this.planoCommonsService.populateModalidadeAux(
						this.plano
					);
					this.plano = this.planoCommonsService.populateHorarioValor(
						this.plano
					);
					this.planoStateService.updateState(this.plano);
					this.router.navigate(["adm", "planos", "novo-plano", "personal"]);
					this.loading = false;
					setTimeout(() => {
						this.cd.detectChanges();
					});
				});
		} else {
			this.plano = this.planoStateService.updatePlanoObj();
			this.plano.tipoPlano = TipoPlano.PLANO_PERSONAL;
			this.plano.diasVencimentoProrata = undefined;
			if (!this.plano.planoRecorrencia) {
				this.plano.planoRecorrencia = {};
			}
			this.planoStateService.updateState(this.plano);
			this.cd.detectChanges();
		}
	}

	voltarParaListagemPlano() {
		this.planoCommonsService.voltarParaListagem();
	}

	salvar() {
		if (
			this.dadosContratuaisComponent &&
			this.dadosContratuaisComponent.form.valid &&
			!this.isSaving
		) {
			this.plano = this.planoStateService.updatePlanoObj();
			this.plano.planoPersonal = true;
			const plano = this.planoCommonsService.convertPlano(this.plano);
			if (!plano.diasVencimentoProrata) {
				this.planoCommonsService.initDiasProrata(plano);
			}
			if (!plano.site) {
				if (plano.termoAceite) {
					plano.termoAceite = null;
				}
			}
			if (plano.planoRecorrencia) {
				if (plano.planoRecorrencia.parcelarAnuidade) {
					plano.planoRecorrencia.parcelaAnuidade = undefined;
				} else {
					plano.planoRecorrencia.parcelasAnuidade = undefined;
				}
				if (plano.planoRecorrencia.parcelasAnuidade) {
					plano.planoRecorrencia.parcelasAnuidade.forEach((parcelaAnuidade) => {
						if (parcelaAnuidade.parcela && parcelaAnuidade.parcela.value) {
							parcelaAnuidade.parcela = parcelaAnuidade.parcela.value;
						}
					});
				}
			}
			this.isSaving = true;
			this.planoService.save(plano).subscribe(
				(response) => {
					this.admRest.notificarNovoPlanoAcesso("NOVO_PLANO_SALVAR");
					this.notificationService.success(
						this.traducoes.getLabel("saved-success")
					);
					this.voltarParaListagemPlano();
					this.isSaving = false;
				},
				(responseError) => {
					this.isSaving = false;
					if (responseError.error) {
						if (responseError.error.meta) {
							this.notificationService.error(
								responseError.error.meta.messageValue
							);
						}
					}
				}
			);
		}
	}
}
