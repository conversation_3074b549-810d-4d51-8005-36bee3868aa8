.modal-content {
	padding: 16px;
}

.row-title {
	display: flex;
	align-items: flex-start;
	gap: 16px;
	align-self: stretch;
}

.column-title {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 10px;
	align-self: stretch;
	padding-right: 16px;
}

span.label {
	color: #51555a;
	font-feature-settings: "clig" off, "liga" off;
	/* pct - title/4 */
	font-family: Poppins;
	font-size: 14px;
	font-style: normal;
	font-weight: 600;
	line-height: 125%; /* 17.5px */
	letter-spacing: 0.25px;
}

span.value {
	color: #51555a;
	font-feature-settings: "clig" off, "liga" off;
	/* pct - body/2-regular */
	font-family: "Nunito Sans";
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 125%; /* 17.5px */
}

.grid-container {
	margin-top: 16px;
	margin-bottom: 16px;
}

::ng-deep .modal-dialog {
	.modal-content {
		width: 1000px !important;
	}
}

.footer {
	display: flex;
	justify-content: flex-end;
	gap: 16px;
	margin-top: 16px;
}
