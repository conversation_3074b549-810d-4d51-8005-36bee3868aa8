@import "src/assets/scss/pacto/plataforma-import.scss";

.pill-status {
	width: 75px;
	height: 24px;
	border-radius: 100px;
	text-transform: capitalize;
}

.pill-status.ativo {
	background: $azulim01;
	padding: 2px 20px;
	color: $azulim03;
}

.pill-status.inativo {
	background: $cinza02;
	padding: 2px 16px;
	color: $cinza05;
}

.texto-subtitulo {
	position: relative;
	flex-grow: 1;
}

.content-wrapper {
	width: 100%;
	display: flex;
	margin-bottom: 30px;
}

.icon-voltar {
	padding: 4px;
	cursor: pointer;
	margin-right: 16px;
}

.content-title {
	width: 100%;
}

.painel-funcionalidades {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
	margin-top: 38px;
	margin-bottom: 32px;
}

.card-funcionalidade {
	width: 408px;
	height: 128px;
	display: flex;
	cursor: pointer;

	.icon {
		width: 72px;
		height: 66px;
		left: 469px;
		top: 884px;
		background: #f3f3f4;
		box-shadow: inset 0px 2px 4px #e4e5e6;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-self: center;
		align-items: center;
		margin-right: 16px;

		i {
			color: $azulimPri;
			font-size: 40px;
		}
	}

	.info {
		width: 264px;
		height: 20px;

		.header {
			color: $pretoPri;
			font-size: 20px;
		}

		.text {
			font-size: 14px;
			line-height: 20px;
			color: $cinza05;
			height: 40px;
		}
	}

	.arrow {
		align-self: center;
		width: 24px;
		height: 24px;
		color: $cinza05;
		font-size: 24px;
	}
}

.margin-left-32 {
	margin-left: 32px;
}

@media (max-width: 768px) {
	.card-funcionalidade {
		margin-top: 32px;
		margin-right: 0;
		margin-left: 0;
	}
}

@media (max-width: 1326px) {
	.card-funcionalidade {
		margin-top: 32px;
	}
}

@media (max-width: 1611px) {
	.card-funcionalidade {
		margin-top: 32px;
	}
}

@media (max-width: 1085px) {
	.card-funcionalidade {
		margin-top: 32px;
		margin-left: 0;
	}
}
