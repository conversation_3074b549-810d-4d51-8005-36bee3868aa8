import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";

import { UiModule } from "ui-kit";
import { TamanhoArmarioFormComponent } from "./components/tamanho-armario-form/tamanho-armario-form.component";
import { TamanhoArmarioComponent } from "./components/tamanho-armario/tamanho-armario.component";
import { ProdutosRoutingModule } from "./produtos-routing.module";
import { ProdutoEstoqueComponent } from "./components/produto-estoque/produto-estoque.component";
import { ProdutoEstoqueFormComponent } from "./components/produto-estoque-form/produto-estoque-form.component";
import { SdkModule } from "sdk";
import { BalancoComponent } from "./components/balanco/balanco.component";
import { BalancoFormComponent } from "./components/balanco-form/balanco-form.component";
import { BalancoModalComponent } from "./components/balanco-modal/balanco-modal.component";
import { CategoriaProdutoComponent } from "./components/categoria-produto/categoria-produto.component";
import { CategoriaProdutoFormComponent } from "./components/categoria-produto-form/categoria-produto-form.component";
import { LayoutModule } from "../layout/layout.module";
import { ComissaoProdutoConfiguracaoFormComponent } from "./components/comissao-produto-configuracao-form/comissao-produto-configuracao-form.component";

@NgModule({
	declarations: [
		TamanhoArmarioComponent,
		TamanhoArmarioFormComponent,
		BalancoComponent,
		BalancoFormComponent,
		BalancoModalComponent,
		ProdutoEstoqueComponent,
		ProdutoEstoqueFormComponent,
		CategoriaProdutoComponent,
		CategoriaProdutoFormComponent,
		ComissaoProdutoConfiguracaoFormComponent,
	],
	imports: [
		SdkModule,
		ProdutosRoutingModule,
		LayoutModule,
		CommonModule,
		NgbModule,
		UiModule,
		ReactiveFormsModule,
	],
	entryComponents: [BalancoModalComponent],
})
export class ProdutosModule {}
