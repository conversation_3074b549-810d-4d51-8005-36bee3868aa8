import { Pipe, PipeTransform } from "@angular/core";
import { DomSanitizer } from "@angular/platform-browser";

@Pipe({ name: "safe" })
export class SafePipe implements PipeTransform {
	constructor(private sanitizer: DomSanitizer) {}

	transform(url, style?: boolean) {
		if (style) {
			return this.sanitizer.bypassSecurityTrustStyle(url);
		}
		return this.sanitizer.bypassSecurityTrustResourceUrl(url);
	}
}
