<div
	[ngClass]="{
		'border-variant': useBorder,
		'justify-between':
			showAvatar && (avatarSize === 'large' || avatarSize === 'xlarge'),
		'flex-row': avatarSize === 'small' || avatarSize === 'medium'
	}"
	class="ds3-checkout">
	<div
		[ngClass]="{
			'flex-row': showInfo || avatarSize === 'small' || avatarSize === 'medium'
		}"
		class="checkout-content">
		<ng-container *ngIf="showAvatar">
			<div class="avatar-container">
				<ds3-avatar
					*ngIf="avatarSize === 'small'"
					class="ds3-avatar"
					size="24"></ds3-avatar>
				<ds3-avatar
					*ngIf="avatarSize === 'medium'"
					class="ds3-avatar"
					size="40"></ds3-avatar>
				<ds3-avatar
					*ngIf="avatarSize === 'large'"
					class="ds3-avatar"
					size="64"></ds3-avatar>
				<ds3-avatar
					*ngIf="avatarSize === 'xlarge'"
					class="ds3-avatar"
					size="88"></ds3-avatar>
			</div>
		</ng-container>
		<ng-container *ngIf="showInfo">
			<ds3-infos [infoArray]="infoArray"></ds3-infos>
		</ng-container>
	</div>
	<div
		*ngIf="buttons > 0 && (avatarSize === 'large' || avatarSize === 'xlarge')"
		class="buttons-container">
		<ng-container *ngIf="buttons >= 1">
			<button
				*ngIf="button1Type === 'flat'"
				ds3-flat-button
				size="sm"
				style="margin-right: 20px">
				{{ button1Name }}
			</button>
			<button
				*ngIf="button1Type === 'outlined'"
				ds3-outlined-button
				size="sm"
				style="margin-right: 20px">
				{{ button1Name }}
			</button>
		</ng-container>
		<ng-container *ngIf="buttons >= 2">
			<button
				*ngIf="button2Type === 'flat'"
				ds3-flat-button
				size="sm"
				style="margin-right: 20px">
				{{ button2Name }}
			</button>
			<button
				*ngIf="button2Type === 'outlined'"
				ds3-outlined-button
				size="sm"
				style="margin-right: 20px">
				{{ button2Name }}
			</button>
		</ng-container>
		<ng-container *ngIf="buttons >= 3">
			<button
				*ngIf="button3Type === 'flat'"
				ds3-flat-button
				size="sm"
				style="margin-right: 20px">
				{{ button3Name }}
			</button>
			<button
				*ngIf="button3Type === 'outlined'"
				ds3-outlined-button
				size="sm"
				style="margin-right: 20px">
				{{ button3Name }}
			</button>
		</ng-container>
	</div>
	<div
		*ngIf="buttons > 0 && (avatarSize === 'small' || avatarSize === 'medium')"
		class="buttons-container-sm">
		<ng-container *ngIf="buttons >= 1">
			<button
				*ngIf="button1Type === 'flat'"
				ds3-flat-button
				size="sm"
				style="margin-right: 20px">
				{{ button1Name }}
			</button>
			<button
				*ngIf="button1Type === 'outlined'"
				ds3-outlined-button
				size="sm"
				style="margin-right: 20px">
				{{ button1Name }}
			</button>
		</ng-container>
		<ng-container *ngIf="buttons >= 2">
			<button
				*ngIf="button2Type === 'flat'"
				ds3-flat-button
				size="sm"
				style="margin-right: 20px">
				{{ button2Name }}
			</button>
			<button
				*ngIf="button2Type === 'outlined'"
				ds3-outlined-button
				size="sm"
				style="margin-right: 20px">
				{{ button2Name }}
			</button>
		</ng-container>
		<ng-container *ngIf="buttons >= 3">
			<button
				*ngIf="button3Type === 'flat'"
				ds3-flat-button
				size="sm"
				style="margin-right: 20px">
				{{ button3Name }}
			</button>
			<button
				*ngIf="button3Type === 'outlined'"
				ds3-outlined-button
				size="sm"
				style="margin-right: 20px">
				{{ button3Name }}
			</button>
		</ng-container>
	</div>
</div>
