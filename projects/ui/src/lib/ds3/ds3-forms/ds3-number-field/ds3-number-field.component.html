<button
	ds3-icon-button
	size="sm"
	[disabled]="disabled || !isDecreasingAllowed()"
	(click)="decreaseValue()"
	id="{{ id }}-decrease">
	<i class="pct pct-minus"></i>
</button>
<input
	class="number-input"
	[type]="type"
	[disabled]="disabled"
	autofocus
	(keyup)="checkValueOnKeyup()"
	[(ngModel)]="value"
	id="{{ id }}-input" />
<button
	ds3-icon-button
	size="sm"
	[disabled]="disabled || !isIncreasingAllowed()"
	(click)="increaseValue()"
	id="{{ id }}-increase">
	<i class="pct pct-plus"></i>
</button>
