import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3SwitchComponent } from "./ds3-switch.component";

describe("Ds3SwitchComponent", () => {
	let component: Ds3SwitchComponent;
	let fixture: ComponentFixture<Ds3SwitchComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3SwitchComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3SwitchComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});

import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3SwitchComponent } from "./ds3-switch.component";

describe("Ds3SwitchComponent", () => {
	let component: Ds3SwitchComponent;
	let fixture: ComponentFixture<Ds3SwitchComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3SwitchComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3SwitchComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
