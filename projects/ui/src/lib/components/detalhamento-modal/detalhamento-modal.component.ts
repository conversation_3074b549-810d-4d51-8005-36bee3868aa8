import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { PactoDataGridConfigDto } from "./dto";

@Component({
	selector: "pacto-detalhamento-modal",
	templateUrl: "./detalhamento-modal.component.html",
	styleUrls: ["./detalhamento-modal.component.scss"],
})
export class DetalhamentoModalComponent implements OnInit {
	@Input() title: string;
	@Input() dados: PactoDataGridConfigDto;

	constructor() {}

	ngOnInit() {
		this.title = "Detalhamento de parcela perdida";
	}
}
