import {
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import {
	<PERSON><PERSON><PERSON>r,
	ControlValueAccessor,
	FormControl,
	FormControlDirective,
	NG_VALUE_ACCESSOR,
} from "@angular/forms";
import createNumberMask from "text-mask-addons/dist/createNumberMask";

@Component({
	selector: "pacto-column-input",
	templateUrl: "./column-input.component.html",
	styleUrls: ["./column-input.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: ColumnInputComponent,
			multi: true,
		},
	],
})
export class ColumnInputComponent implements ControlValueAccessor, OnInit {
	@Input() formControlName: string;
	@ViewChild(FormControlDirective, { static: true })
	formControlDirective: FormControlDirective;
	@Input() formControl: FormControl;
	@Input() id: string;
	@Input() placeholder = "";
	@Input() errorMsg = null;
	@Input() selectOnFocus = false;
	@Input() enableClearInput = true;
	@Input() inputTextMask;
	@Input() type = "text";
	@Input() decimalsPrecision = 2;
	@Input() prefix = "";
	@Input() maxlength: number;
	@Input() max: number;
	@Input() min = 0;
	@Input() disabledControl = false;
	@Output() valueChanges: EventEmitter<any> = new EventEmitter<any>();
	@ViewChild("input", { static: true }) input: ElementRef;

	get control(): any {
		return (
			this.formControl ||
			this.controlContainer.control.get(this.formControlName)
		);
	}

	constructor(private controlContainer: ControlContainer) {}

	ngOnInit() {
		this.setUpId();
		if (this.disabledControl) {
			this.control.disable();
		} else {
			this.control.enable();
		}
	}

	get disabled() {
		return this.formControl.disabled;
	}

	get isInvalid() {
		return (
			this.formControl && this.formControl.touched && !this.formControl.valid
		);
	}

	get showError() {
		return this.isInvalid && !this.disabled;
	}

	get showClearInput() {
		if (this.formControl) {
			return (
				this.enableClearInput &&
				this.control.value &&
				!this.showError &&
				!this.disabled
			);
		}
	}

	focus() {
		this.input.nativeElement.focus();
	}

	clearHandler() {
		this.formControl.setValue("");
	}

	private setUpId() {
		if (!this.id) {
			const rdn = Math.trunc(Math.random() * 1000);
			this.id = `input-component-${rdn}`;
		}
	}

	registerOnTouched(fn: any): void {
		if (this.formControlDirective) {
			this.formControlDirective.valueAccessor.registerOnTouched(fn);
		}
	}

	registerOnChange(fn: any): void {
		if (this.formControlDirective) {
			this.formControlDirective.valueAccessor.registerOnChange(fn);
		}
	}

	writeValue(obj: any): void {
		if (this.formControlDirective) {
			this.formControlDirective.valueAccessor.writeValue(obj);
		}
	}

	setDisabledState(isDisabled: boolean): void {
		if (this.formControlDirective) {
			this.formControlDirective.valueAccessor.setDisabledState(isDisabled);
		}
	}
}
