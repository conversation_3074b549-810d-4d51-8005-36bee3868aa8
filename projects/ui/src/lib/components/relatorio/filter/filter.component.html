<div *ngIf="true" class="filter-wrapper">
	<div
		ngbDropdown
		#filterDropdown="ngbDropdown"
		class="d-inline-block"
		[autoClose]="false"
		[placement]="'bottom-right'">
		<button
			#filterToggleButton
			class="btn btn-primary btn-icon"
			id="filtros-dropdown"
			ngbDropdownToggle>
			<i class="pct pct-filter"></i>
			<span class="icon-drop">
				<i class="fa fa-angle-up"></i>
				<i class="fa fa-angle-down"></i>
			</span>
		</button>
		<div ngbDropdownMenu aria-labelledby="filtros-dropdown">
			<pacto-data-grid-filter
				#dataGridFilter
				[config]="filterConfig"
				(filter)="filterHandler($event)"
				(configUpdate)="
					alterfilterConfigUpdate($event)
				"></pacto-data-grid-filter>
		</div>
	</div>
</div>
