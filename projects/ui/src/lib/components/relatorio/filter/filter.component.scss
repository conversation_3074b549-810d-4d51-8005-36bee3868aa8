@import "projects/ui/assets/import.scss";

:host {
	display: block;
	background-color: #fff;
}

.table-content,
.pacto-table-title-block i.pct {
	font-size: 16px;
}

.pacto-table-title-block {
	position: relative;
	padding: 30px 30px 15px;
	display: flex;

	.title-aux-wrapper {
		margin-right: 30px;
	}

	.table-title {
		font-size: 16px;
		font-weight: 700;
		color: rgb(51, 51, 51);
	}

	.table-description {
		font-size: 13px;
		font-weight: rgb(165, 165, 165);
		font-weight: 300;
	}

	.search {
		position: relative;
		flex-grow: 1;

		input {
			width: 230px;
			padding-left: 30px;
		}

		i.pct {
			position: absolute;
			left: 10px;
			top: 12px;
		}
	}
}

@media (max-width: 1024px) {
	.search {
		display: none;
	}

	.pacto-table-title-block {
		justify-content: space-between;
	}
}

.table-content {
	padding: 0px 30px 30px;
	position: relative;
}

pacto-table-renderer {
	display: block;
	margin-top: 20px;
}

.exportar-dropdown {
	.item {
		line-height: 24px;
		padding: 5px 20px;
		cursor: pointer;

		&:hover {
			background-color: #eee;
		}
	}
}

.footer-row {
	display: flex;
	margin-top: 20px;
	padding-top: 15px;
	border-top: 1px solid #f1f1f1;
	flex-direction: row-reverse;

	> * {
		display: block;
		margin-left: 20px;
	}

	pacto-select .form-group {
		margin-bottom: 0px;
	}
}

.simple-total-row {
	background-color: #f9f9f9;
	border-top: 1px solid #ededed;
	border-bottom: 1px solid #ededed;
	font-size: 14px;
	text-align: center;
	font-weight: bold;
	line-height: 30px;
	margin-top: 14px;
	color: #7f7f7f;
}

.total-values {
	line-height: 32px;
	color: #9d9d9d;
	font-weight: bold;
	margin-right: 10px;
	font-size: 13px;

	.value {
		padding: 0px 3px;
	}
}

.novo-botao {
	margin-left: 15px;
}

.actions {
	display: flex;
}

.dropdown-toggle::after {
	display: none;
}

.btn.btn-primary {
	position: relative;

	.icon-drop {
		position: absolute;
		top: 0;
		right: 0;
		width: 35px;
		height: 38px;
		line-height: 38px;
		text-align: center;
	}

	&:hover,
	&:focus {
		background: $azulim05;
		border: 1px solid $azulim05;
	}

	&:active {
		background: $azulim05;
		border: 1px solid $azulim05;
	}
}

.btn-primary,
.btn.btn-primary:focus,
.show > .btn-primary.dropdown-toggle,
.btn-primary.disabled {
	background: $azulim05;
	border: 1px solid $azulim05;
}

.btn-icon {
	padding-right: 35px;
}

.fa-angle-up {
	display: none;
}

.fa-angle-down {
	display: inline-block;
}

.show {
	.fa-angle-down {
		display: none;
	}

	.fa-angle-up {
		display: inline-block;
	}
}

.show.dropdown {
	.fa-angle-down {
		display: none;
	}

	&.light {
		color: #b4b7bb;
		border-color: #b4b7bb;
	}

	.command-buttons {
		display: flex;
		margin-right: 85px;
		margin-left: 30px;

		.command {
			vertical-align: top;
			line-height: 24px;
			border-radius: 4px;
			padding: 0px 5px;
			color: #a1a5aa;
			font-size: 12px;
			cursor: pointer;
			margin-right: 10px;
			height: 32px;
			margin-top: 4px;
			background-color: #fafafa;
			border: 1px solid #a1a5aa;

			&:hover {
				background-color: rgba(243, 243, 243, 0.5);
			}

			span {
				vertical-align: top;

				&.icone {
					line-height: 29px;
					margin-right: 5px;

					i {
						font-size: 16px;
					}
				}

				&.texto {
					line-height: 20px;
				}
			}
		}
	}
}
