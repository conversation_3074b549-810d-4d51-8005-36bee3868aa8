<div class="pacto-tabs-wrapper">
	<div
		[ngClass]="{
			flexwrap: !tabsJustify,
			justify: tabsJustify
		}"
		class="tabs">
		<div
			(click)="tabClickHandler($event, index)"
			*ngFor="let tab of tabNames; let index = index"
			[id]="idTab(index)"
			[ngClass]="{
				active: index === tabIndex,
				justify: tabsJustify
			}"
			class="tab">
			<i class="{{ tabIcons[index] }}" style="margin-right: 5px"></i>
			<img
				*ngIf="tabImages[index]"
				height="26"
				src="{{ tabImages[index] }}"
				width="26" />
			{{ tab }}
			<span class="total-value">{{ tabTotals[index] }}</span>
		</div>

		<div [ngClass]="{ spacer: !tabsJustify }"></div>

		<pacto-cat-button
			(click)="action.emit(true)"
			*ngIf="showAction"
			[iconPosition]="iconPosition"
			[icon]="actionIcon"
			[id]="actionId"
			[label]="actionLabel"></pacto-cat-button>
	</div>

	<div class="tab-content">
		<ng-container *ngTemplateOutlet="template"></ng-container>
	</div>
</div>
