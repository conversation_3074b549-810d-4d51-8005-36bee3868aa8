import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { AbstractControl, FormControl, FormGroup } from "@angular/forms";
import { debounceTime } from "rxjs/operators";

@Component({
	selector: "pacto-filtro-checkbox",
	templateUrl: "./filtro-checkbox.component.html",
	styleUrls: ["./filtro-checkbox.component.scss"],
})
export class FiltroCheckboxComponent implements OnInit {
	@Output() checksBoxChange: EventEmitter<any> = new EventEmitter<any>();
	@Output() checkAllEvent: EventEmitter<any> = new EventEmitter<any>();
	@Output() pageChangeEvent: EventEmitter<any> = new EventEmitter();
	@Output() pageSizeChangeEvent: EventEmitter<any> = new EventEmitter();
	@Output() searchEvent: EventEmitter<any> = new EventEmitter();
	@Input() formGroup: FormGroup = new FormGroup({
		checkAllForm: new FormControl(),
		searchForm: new FormControl(),
	});
	@Input() formsControlsChecksBox: Array<{ id: number; form: FormControl }> =
		new Array<{ id: number; form: FormControl }>();
	@Input() itens: Array<any>;
	@Input() data: any = {
		content: Array<any>(),
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		number: 0,
	};
	@Input() placeHolderInputBusca = "Busque por nome";
	@Input() labelInputNumber = "";
	@Input() placeHolderInputNumber = "";
	@Input() nameFormInputNumber = "";
	@Input() maxlengthInputNumber = "5";
	@Input() idKey: number;
	@Input() labelKey = "";
	linesIndexItens = new Array<{ indexStart: number; indexEnd: number }>();
	page = 1;
	size = 16;
	ngbPage = 1;
	pageSizeControl: FormControl = new FormControl(16);

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.pageSizeControl.valueChanges.subscribe((pageSize) => {
			this.pageChangeSize(pageSize);
		});
		this.formGroup
			.get("searchForm")
			.valueChanges.pipe(debounceTime(500))
			.subscribe(() => {
				const searchTerm = this.formGroup
					.get("searchForm")
					.value.toLowerCase()
					.trim();
				this.searchEvent.emit(searchTerm);
			});
		this.formGroup.get("checkAllForm").valueChanges.subscribe((value) => {
			this.checkAll(value);
		});
		this.cd.detectChanges();
	}

	getArrayLines() {
		if (this.data.content.length > 0) {
			this.linesIndexItens.splice(0);
			const qtdLines = Math.ceil(this.data.content.length / 4);
			let count = 0;
			for (let i = 1; i <= qtdLines; i++) {
				this.linesIndexItens.push({ indexStart: count, indexEnd: count + 4 });
				count += 4;
			}
		}
		return this.linesIndexItens;
	}

	pageChangeHandler(page) {
		if (!isNaN(page)) {
			this.page = page;
			this.pageChangeEvent.emit(page);
		}
	}

	pageChangeSize(size) {
		if (!isNaN(size)) {
			this.size = size;
			this.pageSizeChangeEvent.emit(size);
		}
	}

	convertToFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	checkRow(item) {
		const index = this.formsControlsChecksBox.findIndex(
			(f) => f.id === item[this.idKey]
		);
		const newCheckValue = !this.formsControlsChecksBox[index].form.value;
		this.formsControlsChecksBox[index].form.setValue(newCheckValue);
		this.checksBoxChange.emit(this.formsControlsChecksBox);
	}

	isChecked(item) {
		const index = this.formsControlsChecksBox.findIndex(
			(f) => f.id === item[this.idKey]
		);
		return this.formsControlsChecksBox[index].form.value;
	}

	checkAll(value) {
		this.formsControlsChecksBox.forEach((f) => {
			f.form.setValue(value);
		});
		if (this.formsControlsChecksBox.length < this.data.totalElements) {
			this.checkAllEvent.emit(value);
		}
		this.checksBoxChange.emit(this.formsControlsChecksBox);
	}

	getFormControl(item): FormControl {
		const index = this.formsControlsChecksBox.findIndex(
			(f) => f.id === item[this.idKey]
		);
		if (index >= 0) {
			return this.formsControlsChecksBox[index].form;
		} else {
			const form = new FormControl();
			form.setValue(false);
			this.formsControlsChecksBox.push({ id: item[this.idKey], form });
			this.checksBoxChange.emit(this.formsControlsChecksBox);
			return form;
		}
	}

	reloadData() {
		// this.formsControlsChecksBox.splice(0);
		this.data.content.forEach((item) => {
			const index = this.formsControlsChecksBox.findIndex(
				(f) => f.id === item[this.idKey]
			);
			if (index === -1) {
				const form = new FormControl();
				form.setValue(false);
				this.formsControlsChecksBox.push({ id: item[this.idKey], form });
			}
		});
		this.checksBoxChange.emit(this.formsControlsChecksBox);
		this.cd.detectChanges();
	}

	getItemLabel(item: any) {
		if (this.labelKey.includes(".")) {
			// Percorrer os atributos separados por ponto ate chegar no label, por exemplo: labelKey 'pessoa.nome', primeiro o item receberá 'pessoa' e por fim 'nome'
			let itemLabel = {};
			Object.assign(itemLabel, item);
			this.labelKey.split(".").forEach((key) => {
				itemLabel = itemLabel[key];
			});
			return itemLabel;
		} else {
			return item[this.labelKey];
		}
	}
}
