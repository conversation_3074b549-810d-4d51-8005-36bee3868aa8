import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Output,
	EventEmitter,
} from "@angular/core";

@Component({
	selector: "pacto-cat-layout-simples",
	templateUrl: "./cat-layout-simples.component.html",
	styleUrls: ["./cat-layout-simples.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatLayoutSimplesComponent implements OnInit {
	@Output() logoClick: EventEmitter<boolean> = new EventEmitter();

	constructor() {}

	ngOnInit() {}

	logoClickHandler() {
		this.logoClick.emit(true);
	}
}
