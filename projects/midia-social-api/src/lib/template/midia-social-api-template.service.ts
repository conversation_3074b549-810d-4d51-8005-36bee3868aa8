import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { MidiaSocialApiBaseService } from '../midia-social-api-base.service';
import { MidiaSocialApiModule } from '../midia-social-api.module';
import { TemplateWhatsapp } from './midia-social-api-template.model';

@Injectable({
  providedIn: MidiaSocialApiModule,
})
export class MidiaSocialApiTemplateService {
  constructor(private readonly restService: MidiaSocialApiBaseService) {}

  public getListaTemplates(): Observable<any> {
    return this.restService.get('v1/template/whatsapp');
  }
  public cadastrarTemplate(template): Observable<any> {
    return this.restService.post('v1/template/whatsapp', template);
  }
  public obterPeloCodigo(codigo: number): Observable<any> {
    return this.restService.get(`v1/template/whatsapp/codigo/${codigo}`);
  }
  public getTemplateAprovado(template: Template<PERSON>hatsapp): Observable<any> {
    return this.restService.get(`v1/template/whatsapp/status/aprovado`);
  }
  public excluirTemplate(codTemplate: number): Observable<any> {
    return this.restService.delete(`v1/template/whatsapp/codigo/${codTemplate}`);
  }
}
