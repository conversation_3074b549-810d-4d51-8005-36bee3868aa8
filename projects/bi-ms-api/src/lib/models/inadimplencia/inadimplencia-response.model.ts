export interface InadimplenciaResponseModel {
	totalizador: Array<InadimplenciaTotalizadorJSON>;
	inadimplenciaBi: DadosInadimplenciaItemResponseModel;
	previsaoBi: DadosInadimplenciaItemResponseModel;
	pagasBi: DadosInadimplenciaItemResponseModel;
	canceladasBi: DadosInadimplenciaItemResponseModel;
	inadimplenciaTotalBi: DadosInadimplenciaItemResponseModel;
	previsaoTotalBi: DadosInadimplenciaItemResponseModel;
	pagasTotalBi: DadosInadimplenciaItemResponseModel;
	dadosGraficoInadimplencia: Array<InadimplenciaGraficoResponseModel>;
}

export interface DadosInadimplenciaItemResponseModel {
	label: string;
	valor: number;
	quantidade: number;
	porcentagem: number;
	mesReferencia: number;
	quantidadeAlunos: number;
}

export interface InadimplenciaGraficoResponseModel {
	mes: string;
	valorPago: number;
	valorEmAberto: number;
	inadimplencia: number;
	valorTotal: number;
	quantidadeAlunosPrevisao: number;
	quantidadeAlunosPagas: number;
	quantidadeAlunosInadimplencia: number;
	quantidadeAlunosCanceladas: number;
}

export interface InadimplenciaTotalizadorJSON {
	previsao: DadosInadimplenciaItemResponseModel;
	pagas: DadosInadimplenciaItemResponseModel;
	inadimplencia: DadosInadimplenciaItemResponseModel;
	media: boolean;
}
