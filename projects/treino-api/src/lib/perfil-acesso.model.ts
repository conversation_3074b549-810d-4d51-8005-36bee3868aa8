import {
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "./perfil-acesso-recurso.model";

export interface Perfil {
	id: string;
	nome: string;
	tipo: string;
}

export class PerfilAcessoDetalheDTO {
	id: number;
	nome: string;
	tipo: string;
	recursos: {
		recurso: string;
		tipoPermissoes: string[];
	}[];
	funcionalidades: {
		nome: PerfilAcessoFuncionalidadeNome;
		possuiFuncionalidade: boolean;
	}[];
}

/**
 * Representa um perfil de acesso no sistema treino. Um perfil
 * possui algumas funcionalidades habilitadas e outra não. Além disso,
 * para cada recurso um perfil pode ter diferentes tipos de permissão:
 *
 *   CONSULTAR = 'CONSULTAR', INCLUIR = 'INCLUIR',
 *   EDITAR = 'EDITAR', EXCLUIR = 'EXCLUIR',
 *   TOTAL_EXCETO_EXCLUIR = 'TOTAL_EXCETO_EXCLUIR',
 *   TOTAL = 'TOTAL'
 *
 */
export class PefilAcessoDetalhe {
	id: number;
	nome: string;
	tipo: string;
	recursos: Map<PerfilAcessoRecursoNome, PerfilAcessoRecurso>;
	funcionalidades: Map<PerfilAcessoFuncionalidadeNome, boolean>;

	constructor(dto: PerfilAcessoDetalheDTO) {
		this.nome = dto.nome;
		this.tipo = dto.tipo;
		this.recursos = new Map();
		this.funcionalidades = new Map();

		dto.recursos.forEach((recursoItem) => {
			this.recursos.set(
				recursoItem.recurso as PerfilAcessoRecursoNome,
				new PerfilAcessoRecurso(
					recursoItem.recurso as PerfilAcessoRecursoNome,
					recursoItem.tipoPermissoes as PerfilRecursoPermissoTipo[]
				)
			);
		});

		for (const recurso in dto.recursos) {
			if (dto.recursos.hasOwnProperty(recurso)) {
			}
		}

		dto.funcionalidades.forEach((funcionalidade, index) => {
			if (dto.funcionalidades.hasOwnProperty(index)) {
				if (funcionalidade.nome === "tela_prescricao_treino") {
					this.funcionalidades.set(
						funcionalidade.nome as PerfilAcessoFuncionalidadeNome,
						true
					);
				} else {
					this.funcionalidades.set(
						funcionalidade.nome as PerfilAcessoFuncionalidadeNome,
						funcionalidade.possuiFuncionalidade
					);
				}
			}
		});
	}

	possuiRecurso(recursoNecessario: PerfilAcessoRecurso) {
		let allowed = false;
		if (!recursoNecessario) {
			return true;
		} else if (recursoNecessario.permissao.length === 0) {
			return true;
		} else if (recursoNecessario) {
			const usuario = this.recursos.get(recursoNecessario.recurso);
			const necessario: PerfilAcessoRecurso = recursoNecessario;
			necessario.permissao.forEach((tipo) => {
				if (usuario) {
					allowed = usuario.permissao.includes(tipo) || allowed;
				}
			});
		}
		return allowed;
	}

	possuiFuncionalidade(funcionalidadeNecessario: PerfilAcessoFuncionalidade) {
		let allowed = false;
		if (!funcionalidadeNecessario) {
			return true;
		} else if (funcionalidadeNecessario) {
			const usuarioFuncionalidade = this.funcionalidades.get(
				funcionalidadeNecessario.funcionalidade
			);
			const necessario: PerfilAcessoFuncionalidade = funcionalidadeNecessario;
			if (necessario.permissao === usuarioFuncionalidade) {
				allowed = true;
			} else {
				allowed = false;
			}
		}
		return allowed;
	}
}

export class PerfilAcessoRecurso {
	recurso: PerfilAcessoRecursoNome;
	permissao: PerfilRecursoPermissoTipo[];

	get consultar(): boolean {
		const consultar = this.permissao.includes(
			PerfilRecursoPermissoTipo.CONSULTAR
		);
		const todosExcetoExc = this.permissao.includes(
			PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
		);
		const todos = this.permissao.includes(PerfilRecursoPermissoTipo.TOTAL);
		return consultar || todosExcetoExc || todos;
	}

	get incluir() {
		const incluir = this.permissao.includes(PerfilRecursoPermissoTipo.INCLUIR);
		const todosExcetoExc = this.permissao.includes(
			PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
		);
		const todos = this.permissao.includes(PerfilRecursoPermissoTipo.TOTAL);
		return incluir || todosExcetoExc || todos;
	}

	get editar() {
		const editar = this.permissao.includes(PerfilRecursoPermissoTipo.EDITAR);
		const todosExcetoExc = this.permissao.includes(
			PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
		);
		const todos = this.permissao.includes(PerfilRecursoPermissoTipo.TOTAL);
		return editar || todosExcetoExc || todos;
	}

	get incluirConsultar() {
		const incluirEConsultar = this.permissao.includes(
			PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
		);

		return incluirEConsultar;
	}

	get excluir() {
		const totalExcExcluir = this.permissao.includes(
			PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
		);
		const excluir = this.permissao.includes(PerfilRecursoPermissoTipo.EXCLUIR);
		const todos = this.permissao.includes(PerfilRecursoPermissoTipo.TOTAL);
		if (totalExcExcluir) {
			return false;
		} else {
			return excluir || todos;
		}
	}

	constructor(
		recurso: PerfilAcessoRecursoNome,
		permissao: PerfilRecursoPermissoTipo[]
	) {
		this.recurso = recurso;
		this.permissao = permissao;
	}
}

export class PerfilAcessoFuncionalidade {
	funcionalidade: PerfilAcessoFuncionalidadeNome;
	permissao: boolean;

	get permitir(): boolean {
		return this.permissao;
	}

	constructor(
		funcionalidade: PerfilAcessoFuncionalidadeNome,
		permissao: boolean
	) {
		this.funcionalidade = funcionalidade;
		this.permissao = permissao;
	}
}

export class PefilAcessoAdmDetalhe {
	nome: string;
	funcionalidades: Array<FuncionalidadeAdm>;
	recursos: Array<RecursoAdm>;

	constructor(obj: PerfilUsuarioAdm) {
		this.nome = obj.nome;
		this.recursos = new Array();
		this.funcionalidades = new Array();

		obj.recursos.forEach((recursoItem) => {
			this.recursos.push({
				recurso: recursoItem.recurso as PerfilAcessoRecursoNome,
				referenciaRecurso: recursoItem.recurso as PerfilAcessoRecursoNome,
				tipoPermissoes:
					recursoItem.tipoPermissoes as PerfilRecursoPermissoTipo[],
			});
		});

		for (const recurso in obj.recursos) {
			if (obj.recursos.hasOwnProperty(recurso)) {
			}
		}
	}

	possuiRecurso(recursoNecessario: PerfilAcessoRecurso) {
		let allowed = false;
		if (!recursoNecessario) {
			return true;
		} else if (recursoNecessario.permissao.length === 0) {
			return true;
		} else if (recursoNecessario) {
			const usuario = this.recursos.find(
				(r) => r.recurso === recursoNecessario.recurso
			);
			recursoNecessario.permissao.forEach((tipo) => {
				if (usuario) {
					allowed = usuario.tipoPermissoes.includes(tipo) || allowed;
				}
			});
		}
		return allowed;
	}
}

export interface PerfilUsuarioAdm {
	nome: string;
	funcionalidades: Array<FuncionalidadeAdm>;
	recursos: Array<RecursoAdm>;
}

export interface FuncionalidadeAdm {
	referenciaFuncionalidade: string;
	nome: string;
	possuiFuncionalidade: boolean;
}

export interface RecursoAdm {
	recurso: string;
	referenciaRecurso: string;
	tipoPermissoes: Array<string>;
}
