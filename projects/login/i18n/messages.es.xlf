<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
  <file source-language="pt" datatype="plaintext" original="ng2.template" target-language="es">
    <body>
      <trans-unit id="ngb.alert.close" datatype="html">
        <source>Close</source><target state="new">Close</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/alert/alert.d.ts</context>
          <context context-type="linenumber">2</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.carousel.previous" datatype="html">
        <source>Previous</source><target state="new">Previous</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/carousel/carousel.d.ts</context>
          <context context-type="linenumber">13</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.carousel.next" datatype="html">
        <source>Next</source><target state="new">Next</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/carousel/carousel.d.ts</context>
          <context context-type="linenumber">17</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.datepicker.previous-month" datatype="html">
        <source>Previous month</source><target state="new">Previous month</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/datepicker/datepicker-navigation.d.ts</context>
          <context context-type="linenumber">4</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/datepicker/datepicker-navigation.d.ts</context>
          <context context-type="linenumber">5</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.datepicker.next-month" datatype="html">
        <source>Next month</source><target state="new">Next month</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/datepicker/datepicker-navigation.d.ts</context>
          <context context-type="linenumber">26</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/datepicker/datepicker-navigation.d.ts</context>
          <context context-type="linenumber">27</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.datepicker.select-month" datatype="html">
        <source>Select month</source><target state="new">Select month</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/datepicker/datepicker-navigation-select.d.ts</context>
          <context context-type="linenumber">6</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/datepicker/datepicker-navigation-select.d.ts</context>
          <context context-type="linenumber">7</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.datepicker.select-year" datatype="html">
        <source>Select year</source><target state="new">Select year</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/datepicker/datepicker-navigation-select.d.ts</context>
          <context context-type="linenumber">15</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/datepicker/datepicker-navigation-select.d.ts</context>
          <context context-type="linenumber">16</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.pagination.first" datatype="html">
        <source>««</source><target state="new">««</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/pagination/pagination.d.ts</context>
          <context context-type="linenumber">2</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.pagination.previous" datatype="html">
        <source>«</source><target state="new">«</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/pagination/pagination.d.ts</context>
          <context context-type="linenumber">3</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.pagination.next" datatype="html">
        <source>»</source><target state="new">»</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/pagination/pagination.d.ts</context>
          <context context-type="linenumber">4</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.pagination.last" datatype="html">
        <source>»»</source><target state="new">»»</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/pagination/pagination.d.ts</context>
          <context context-type="linenumber">5</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.pagination.first-aria" datatype="html">
        <source>First</source><target state="new">First</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/pagination/pagination.d.ts</context>
          <context context-type="linenumber">14</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.pagination.previous-aria" datatype="html">
        <source>Previous</source><target state="new">Previous</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/pagination/pagination.d.ts</context>
          <context context-type="linenumber">23</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.pagination.next-aria" datatype="html">
        <source>Next</source><target state="new">Next</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/pagination/pagination.d.ts</context>
          <context context-type="linenumber">41</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.pagination.last-aria" datatype="html">
        <source>Last</source><target state="new">Last</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/pagination/pagination.d.ts</context>
          <context context-type="linenumber">49</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.progressbar.value" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{getPercentValue()}}"/>%</source><target state="new"><x id="INTERPOLATION" equiv-text="{{getPercentValue()}}"/>%</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/progressbar/progressbar.d.ts</context>
          <context context-type="linenumber">6</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.increment-hours" datatype="html">
        <source>Increment hours</source><target state="new">Increment hours</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">9</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.HH" datatype="html">
        <source>HH</source><target state="new">HH</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">12</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.hours" datatype="html">
        <source>Hours</source><target state="new">Hours</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">14</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.decrement-hours" datatype="html">
        <source>Decrement hours</source><target state="new">Decrement hours</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">20</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.increment-minutes" datatype="html">
        <source>Increment minutes</source><target state="new">Increment minutes</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">29</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.MM" datatype="html">
        <source>MM</source><target state="new">MM</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">32</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.minutes" datatype="html">
        <source>Minutes</source><target state="new">Minutes</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">34</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.decrement-minutes" datatype="html">
        <source>Decrement minutes</source><target state="new">Decrement minutes</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">40</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.increment-seconds" datatype="html">
        <source>Increment seconds</source><target state="new">Increment seconds</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">49</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.SS" datatype="html">
        <source>SS</source><target state="new">SS</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">52</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.seconds" datatype="html">
        <source>Seconds</source><target state="new">Seconds</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">54</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.decrement-seconds" datatype="html">
        <source>Decrement seconds</source><target state="new">Decrement seconds</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">60</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.PM" datatype="html">
        <source>PM</source><target state="new">PM</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">68</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ngb.timepicker.AM" datatype="html">
        <source>AM</source><target state="new">AM</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../node_modules/@ng-bootstrap/ng-bootstrap/timepicker/timepicker.d.ts</context>
          <context context-type="linenumber">69</context>
        </context-group>
      </trans-unit>
      <trans-unit id="share-button:compartilhar" datatype="html">
        <source>
        <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span>"/>Compartilhar<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span>"/>
        <x id="START_TAG_SPAN_1" ctype="x-span" equiv-text="&lt;span>"/>
            <x id="START_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;i>"/><x id="CLOSE_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;/i>"/>
        <x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span>"/>
    </source><target state="new">
        <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span>"/>Compartilhar<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span>"/>
        <x id="START_TAG_SPAN_1" ctype="x-span" equiv-text="&lt;span>"/>
            <x id="START_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;i>"/><x id="CLOSE_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;/i>"/>
        <x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span>"/>
    </target>
        
      <context-group purpose="location"><context context-type="sourcefile">../../dist/ui-kit/lib/components/share-button/share-button.component.d.ts</context><context context-type="linenumber">3</context></context-group></trans-unit>
      
      
      
      <trans-unit id="component-relatorio:mostrando" datatype="html">
        <source> Mostrando </source><target state="new"> Mostrando </target>


      <context-group purpose="location"><context context-type="sourcefile">../../dist/ui-kit/lib/components/relatorio/relatorio.component.d.ts</context><context context-type="linenumber">198</context></context-group><context-group purpose="location"><context context-type="sourcefile">../../dist/ui-kit/lib/components/cat-editable-table/cat-table-editable.component.d.ts</context><context context-type="linenumber">131</context></context-group></trans-unit>
      <trans-unit id="component-relatorio:de" datatype="html">
        <source> de </source><target state="new"> de </target>


      <context-group purpose="location"><context context-type="sourcefile">../../dist/ui-kit/lib/components/relatorio/relatorio.component.d.ts</context><context context-type="linenumber">200</context></context-group><context-group purpose="location"><context context-type="sourcefile">../../dist/ui-kit/lib/components/cat-editable-table/cat-table-editable.component.d.ts</context><context context-type="linenumber">133</context></context-group></trans-unit>
      <trans-unit id="component-relatorio:sem-dados-disponiveis" datatype="html">
        <source> <x id="INTERPOLATION" equiv-text="{{emptyStateMessage}}"/> </source><target state="new"> <x id="INTERPOLATION" equiv-text="{{emptyStateMessage}}"/> </target>

      <context-group purpose="location"><context context-type="sourcefile">../../dist/ui-kit/lib/components/relatorio/relatorio-renderer/relatorio-renderer.component.d.ts</context><context context-type="linenumber">182</context></context-group></trans-unit>
      <trans-unit id="component-relatorio:carregando-dados" datatype="html">
        <source> Carregando dados...</source><target state="new"> Carregando dados...</target>

      <context-group purpose="location"><context context-type="sourcefile">../../dist/ui-kit/lib/components/relatorio/relatorio-renderer/relatorio-renderer.component.d.ts</context><context context-type="linenumber">185</context></context-group></trans-unit>
      <trans-unit id="pacto-filtro-checkbox:label-selecionar-todos" datatype="html">
        <source>Selecionar todos</source><target state="new">Selecionar todos</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/components/filtro-checkbox/filtro-checkbox.component.d.ts</context>
          <context context-type="linenumber">13</context>
        </context-group>
      </trans-unit>
      <trans-unit id="pacto-filtro-checkbox:mostrando" datatype="html">
        <source> Mostrando </source><target state="new"> Mostrando </target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/components/filtro-checkbox/filtro-checkbox.component.d.ts</context>
          <context context-type="linenumber">63</context>
        </context-group>
      </trans-unit>
      <trans-unit id="pacto-filtro-checkbox:de" datatype="html">
        <source> de </source><target state="new"> de </target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/components/filtro-checkbox/filtro-checkbox.component.d.ts</context>
          <context context-type="linenumber">65</context>
        </context-group>
      </trans-unit>
      <trans-unit id="dialog-confirm:btn-cancel" datatype="html">
        <source>Cancelar</source><target state="new">Cancelar</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/dialog/components/confirm-dialog-delete/confirm-dialog-delete.component.d.ts</context>
          <context context-type="linenumber">17</context>
        </context-group>
      </trans-unit>
      <trans-unit id="buttons:cancelar" datatype="html">
        <source> Cancelar </source><target state="new"> Cancelar </target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/dialog/components/confirm-dialog/confirm-dialog.component.d.ts</context>
          <context context-type="linenumber">18</context>
        </context-group>
      </trans-unit>
      <trans-unit id="buttons:remover" datatype="html">
        <source>Remover</source><target state="new">Remover</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/dialog/components/confirm-dialog/confirm-dialog.component.d.ts</context>
          <context context-type="linenumber">24</context>
        </context-group>
      </trans-unit>
      <trans-unit id="plataforma-global-menu:quick-actions:alunos" datatype="html">
        <source> Alunos </source><target state="new"> Alunos </target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/layout/cat-platform-menu-v2/components/cat-topbar/cat-topbar.component.d.ts</context>
          <context context-type="linenumber">15</context>
        </context-group>
      </trans-unit><trans-unit id="alunos-recentes:modal-title" datatype="html">
        <source>Alunos favoritos</source><target state="new">Alunos favoritos</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/layout/cat-platform-menu-v2/components/cat-options-alunos-recentes/cat-options-alunos-recentes.component.d.ts</context>
          <context context-type="linenumber">2</context>
        </context-group>
      </trans-unit><trans-unit id="alunos-recentes:alunos-favoritos-title" datatype="html">
        <source>Favoritos</source><target state="new">Favoritos</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/layout/cat-platform-menu-v2/components/cat-options-alunos-recentes/cat-options-alunos-recentes.component.d.ts</context>
          <context context-type="linenumber">6</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/layout/cat-platform-menu-v2/components/cat-options-alunos-recentes/cat-options-alunos-recentes.component.d.ts</context>
          <context context-type="linenumber">67</context>
        </context-group>
      </trans-unit><trans-unit id="alunos-recentes:nenhum-aluno-favorito-label" datatype="html">
        <source>Nenhum
			aluno adicionado na sua lista de favoritos. Você pode adicionar até 3 (três) alunos aqui. </source><target state="new">Nenhum
			aluno adicionado na sua lista de favoritos. Você pode adicionar até 3 (três) alunos aqui. </target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/layout/cat-platform-menu-v2/components/cat-options-alunos-recentes/cat-options-alunos-recentes.component.d.ts</context>
          <context context-type="linenumber">8</context>
        </context-group>
      </trans-unit><trans-unit id="alunos-recentes:salvar-button" datatype="html">
        <source>Salvar</source><target state="new">Salvar</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/layout/cat-platform-menu-v2/components/cat-options-alunos-recentes/cat-options-alunos-recentes.component.d.ts</context>
          <context context-type="linenumber">25</context>
        </context-group>
      </trans-unit><trans-unit id="alunos-recentes:nenhum-aluno-recente-label" datatype="html">
        <source>Nenhum aluno
			recente</source><target state="new">Nenhum aluno
			recente</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/layout/cat-platform-menu-v2/components/cat-options-alunos-recentes/cat-options-alunos-recentes.component.d.ts</context>
          <context context-type="linenumber">69</context>
        </context-group>
      </trans-unit>
      <trans-unit id="confirm-goback-title" datatype="html">
        <source>Confirmar retorno</source><target state="new">Confirmar retorno</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/components/cat-stepper-simple/components/cat-stepper-simple/cat-stepper-simple.component.d.ts</context>
          <context context-type="linenumber">29</context>
        </context-group>
      </trans-unit>
      <trans-unit id="confirm-goback-body" datatype="html">
        <source>Ao confirmar as alterações serão perdidas, deseja prosseguir?</source><target state="new">Ao confirmar as alterações serão perdidas, deseja prosseguir?</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/components/cat-stepper-simple/components/cat-stepper-simple/cat-stepper-simple.component.d.ts</context>
          <context context-type="linenumber">30</context>
        </context-group>
      </trans-unit>
      <trans-unit id="confirm-goback-proceed" datatype="html">
        <source>Prosseguir</source><target state="new">Prosseguir</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/components/cat-stepper-simple/components/cat-stepper-simple/cat-stepper-simple.component.d.ts</context>
          <context context-type="linenumber">31</context>
        </context-group>
      </trans-unit>
      <trans-unit id="relatorio-atividade-professores:data-inicio:filtro" datatype="html">
        <source>Data Início</source><target state="new">Data Início</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/components/log/tabela-log/tabela-log.component.d.ts</context>
          <context context-type="linenumber">89</context>
        </context-group>
      </trans-unit>
      <trans-unit id="relatorio-atividade-professores:data-fim:filtro" datatype="html">
        <source>Data Fim</source><target state="new">Data Fim</target>
        <context-group purpose="location">
          <context context-type="sourcefile">../../dist/ui-kit/lib/components/log/tabela-log/tabela-log.component.d.ts</context>
          <context context-type="linenumber">90</context>
        </context-group>
      </trans-unit><trans-unit id="login:link-patrocinado" datatype="html">
        <source>
                        <x id="START_TAG_LOGIN-CAT-ICON" ctype="x-login-cat-icon" equiv-text="&lt;login-cat-icon>"/><x id="CLOSE_TAG_LOGIN-CAT-ICON" ctype="x-login-cat-icon" equiv-text="&lt;/login-cat-icon>"/> Link patrocinado
                    </source><target state="new">
                        <x id="START_TAG_LOGIN-CAT-ICON" ctype="x-login-cat-icon" equiv-text="&lt;login-cat-icon>"/><x id="CLOSE_TAG_LOGIN-CAT-ICON" ctype="x-login-cat-icon" equiv-text="&lt;/login-cat-icon>"/> Link patrocinado
                    </target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/login/login.component.html</context><context context-type="linenumber">15</context></context-group></trans-unit><trans-unit id="login:slogan-pacto" datatype="html">
        <source>A gente se envolve <x id="START_TAG_LOGIN-CAT-ICON" ctype="x-login-cat-icon" equiv-text="&lt;login-cat-icon>"/><x id="CLOSE_TAG_LOGIN-CAT-ICON" ctype="x-login-cat-icon" equiv-text="&lt;/login-cat-icon>"/></source><target state="new">A gente se envolve <x id="START_TAG_LOGIN-CAT-ICON" ctype="x-login-cat-icon" equiv-text="&lt;login-cat-icon>"/><x id="CLOSE_TAG_LOGIN-CAT-ICON" ctype="x-login-cat-icon" equiv-text="&lt;/login-cat-icon>"/></target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/login/login.component.html</context><context context-type="linenumber">55</context></context-group></trans-unit><trans-unit id="login:placeholder-usuario-email" datatype="html">
        <source>E-mail</source><target state="new">E-mail</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context>
          <context context-type="linenumber">3</context>
        </context-group>
      </trans-unit><trans-unit id="login:form-login-usuario-inválido" datatype="html">
        <source>Informe um e-mail válido</source><target state="new">Digite um nome de usuário válido!</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">5</context></context-group><context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">11</context></context-group></trans-unit><trans-unit id="login:placeholder-usuario-telefone" datatype="html">
        <source>Celular</source><target state="new">Celular</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">8</context></context-group><context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">9</context></context-group></trans-unit><trans-unit id="login:form-login-placeholder-senha" datatype="html">
        <source>Senha</source><target state="new">Senha</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">14</context></context-group></trans-unit><trans-unit id="login:form-login-senha-inválido" datatype="html">
        <source>Senha inválida!</source><target state="new">Senha inválida!</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">17</context></context-group></trans-unit><trans-unit id="login:form-login-esqueci-senha" datatype="html">
        <source>Esqueci minha senha</source><target state="new">Esqueci minha senha</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">20</context></context-group></trans-unit><trans-unit id="login:form-login-entrar" datatype="html">
        <source>Entrar</source><target state="new">Entrar</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">40</context></context-group></trans-unit><trans-unit id="login:form-login-voltar" datatype="html">
        <source>Voltar</source><target state="new">Voltar</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">47</context></context-group></trans-unit><trans-unit id="form-login:usuario-nao-encontrado" datatype="html">
        <source>Usuário não encontrado!</source><target state="new">Usuário não encontrado</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">73</context></context-group></trans-unit><trans-unit id="form-login:email-inativo" datatype="html">
        <source>E-mail não foi ativado!</source><target state="new">E-mail não foi ativado</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">74</context></context-group></trans-unit><trans-unit id="form-login:celular-inativo" datatype="html">
        <source>Celular não foi ativado!</source><target state="new">Celular não foi ativado</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">75</context></context-group></trans-unit><trans-unit id="form-login:senha-invalida" datatype="html">
        <source>Senha inválida!</source><target state="new">Senha Inválida</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">76</context></context-group></trans-unit><trans-unit id="form-login:erro-captcha" datatype="html">
        <source>Erro na validação do captcha!</source><target state="new">Erro token do captcha</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/form-login/form-login.component.html</context><context context-type="linenumber">77</context></context-group></trans-unit>
      <trans-unit id="login:recover-psw-title" datatype="html">
        <source>Esqueceu sua senha?</source><target state="new">Esqueceu sua senha?</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/recover-password/recover-psw.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
      </trans-unit>
      <trans-unit id="login:recover-psw-hint" datatype="html">
        <source>
        Sem problemas! Informe seus dados abaixo e encaminharemos um e-mail
        com as instruções para que você possa redefinir sua senha.
    </source><target state="new">
        Sem problemas! Informe seus dados abaixo e encaminharemos um e-mail
        com as instruções para que você possa redefinir sua senha.
    </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/recover-password/recover-psw.component.html</context>
          <context context-type="linenumber">5</context>
        </context-group>
      </trans-unit>
      <trans-unit id="login:recover-psw-email" datatype="html">
        <source>E-mail</source><target state="new">E-mail</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/recover-password/recover-psw.component.html</context>
          <context context-type="linenumber">14</context>
        </context-group>
      </trans-unit>
      <trans-unit id="login:recover-psw-email-error" datatype="html">
        <source>E-mail inválido</source><target state="new">E-mail inválido</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/recover-password/recover-psw.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
      </trans-unit>
      <trans-unit id="login:recover-psw-btn-submit" datatype="html">
        <source>Solicitar nova senha</source><target state="new">Solicitar nova senha</target>
        
      <context-group purpose="location"><context context-type="sourcefile">src/app/components/recover-password/recover-psw.component.html</context><context context-type="linenumber">35</context></context-group></trans-unit><trans-unit id="login:recover-psw-btn-voltar" datatype="html">
        <source>Voltar</source><target state="new">Voltar</target>
        
      <context-group purpose="location"><context context-type="sourcefile">src/app/components/recover-password/recover-psw.component.html</context><context context-type="linenumber">41</context></context-group></trans-unit>
      <trans-unit id="login:recover-psw-captcha-error" datatype="html">
        <source>Captcha não informado!</source><target state="new">Captcha não informado!</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/recover-password/recover-psw.component.html</context><context context-type="linenumber">52</context></context-group></trans-unit><trans-unit id="login:recover-psw-solicitacao-enviada" datatype="html">
        <source>Solicitação enviada!</source><target state="new">Solicitação enviada!</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/recover-psw-success/recover-psw-success.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
      </trans-unit><trans-unit id="login:recover-psw-solicitacao-hint" datatype="html">
        <source>
        Para concluir a recuperação de senha, siga as instruções enviadas para seu e-mail
        <x id="START_TAG_STRONG" ctype="x-strong" equiv-text="&lt;strong>"/><x id="INTERPOLATION" equiv-text="{{email}}"/><x id="CLOSE_TAG_STRONG" ctype="x-strong" equiv-text="&lt;/strong>"/>
    </source><target state="new">
        Para concluir a recuperação de senha, siga as instruções enviadas para seu e-mail
        <x id="START_TAG_STRONG" ctype="x-strong" equiv-text="&lt;strong>"/><x id="INTERPOLATION" equiv-text="{{email}}"/><x id="CLOSE_TAG_STRONG" ctype="x-strong" equiv-text="&lt;/strong>"/>
    </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/recover-psw-success/recover-psw-success.component.html</context>
          <context context-type="linenumber">5</context>
        </context-group>
      </trans-unit><trans-unit id="login:finalizar" datatype="html">
        <source>
    Finalizar
</source><target state="new">
    Finalizar
</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/recover-psw-success/recover-psw-success.component.html</context>
          <context context-type="linenumber">11</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-psw-title" datatype="html">
        <source>Altere sua senha</source><target state="new">Altere sua senha</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw/change-psw.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-psw-hint" datatype="html">
        <source>
    <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span>"/>
        Estamos quase lá!<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br/>"/>
        Informe abaixo sua nova senha.
    <x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span>"/>
</source><target state="new">
    <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span>"/>
        Estamos quase lá!<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br/>"/>
        Informe abaixo sua nova senha.
    <x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span>"/>
</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw/change-psw.component.html</context>
          <context context-type="linenumber">4</context>
        </context-group>
      </trans-unit><trans-unit id="login:new-psw-label" datatype="html">
        <source>Nova senha</source><target state="new">Nova senha</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw/change-psw.component.html</context>
          <context context-type="linenumber">12</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-psw-new-psw" datatype="html">
        <source>Digite a nova senha</source><target state="new">Digite a nova senha</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw/change-psw.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-psw-new-psw-error" datatype="html">
        <source>Digite a nova senha</source><target state="new">Digite a nova senha</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw/change-psw.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
      </trans-unit><trans-unit id="login:confirm-new-psw-label" datatype="html">
        <source>Confirmar nova senha</source><target state="new">Confirmar nova senha</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw/change-psw.component.html</context>
          <context context-type="linenumber">20</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-psw-confirm-psw" datatype="html">
        <source>Confirme a nova senha</source><target state="new">Confirme a nova senha</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw/change-psw.component.html</context>
          <context context-type="linenumber">21</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-psw-confirm-psw-error-required" datatype="html">
        <source>
            Digite a nova senha
        </source><target state="new">
            Digite a nova senha
        </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw/change-psw.component.html</context>
          <context context-type="linenumber">27</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-psw-confirm-psw-error-diff" datatype="html">
        <source>
            As senhas se diferem
        </source><target state="new">
            As senhas se diferem
        </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw/change-psw.component.html</context>
          <context context-type="linenumber">31</context>
        </context-group>
      </trans-unit><trans-unit id="login:psw-strongth-hint" datatype="html">
        <source>A senha deve conter no mínimo 8 caracteres, um número, uma letra maiúscula, uma letra minúscula e um caractere especial.</source><target state="new">A senha deve conter no mínimo 8 caracteres, um número, uma letra maiúscula, uma letra minúscula e um caractere especial.</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw/change-psw.component.html</context>
          <context context-type="linenumber">37</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-psw-submit" datatype="html">
        <source>Alterar senha
    </source><target state="new">Alterar senha
    </target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/change-psw/change-psw.component.html</context><context context-type="linenumber">40</context></context-group></trans-unit><trans-unit id="login:change-psw-password-reset-success" datatype="html">
        <source>Senha alterada com sucesso!</source><target state="new">Senha alterada com sucesso!</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/change-psw/change-psw.component.html</context><context context-type="linenumber">49</context></context-group></trans-unit><trans-unit id="login:weak-password-erro" datatype="html">
        <source>Sua senha está muito fraca, verifique se ela atende todos os requisitos.</source><target state="new">Sua senha está muito fraca, verifique se ela atende todos os requisitos.</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw/change-psw.component.html</context>
          <context context-type="linenumber">50</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-psw-success-title" datatype="html">
        <source>Senha alterada</source><target state="new">Senha alterada</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw-success/change-psw-success.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-psw-success-hint" datatype="html">
        <source>Pronto! Sua senha foi redefinida, clique abaixo para efetuar o login.</source><target state="new">Pronto! Sua senha foi redefinida, clique abaixo para efetuar o login.</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw-success/change-psw-success.component.html</context>
          <context context-type="linenumber">5</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-psw-success-btn" datatype="html">
        <source>
    Login
</source><target state="new">
    Login
</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-psw-success/change-psw-success.component.html</context>
          <context context-type="linenumber">7</context>
        </context-group>
      </trans-unit><trans-unit id="login:dashboard-logo-pacto" datatype="html">
        <source>Logo pacto</source><target state="new">Logo pacto</target>
        
      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">6</context></context-group></trans-unit><trans-unit id="login:dashboard-btn-central-ajuda" datatype="html">
        <source>Central de ajuda</source><target state="new">Central de ajuda</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">15</context></context-group></trans-unit><trans-unit id="login:dashboard-btn-logout" datatype="html">
        <source>Sair</source><target state="new">Sair</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">20</context></context-group></trans-unit><trans-unit id="dashboard:user-greetting" datatype="html">
        <source>Olá, <x id="INTERPOLATION" equiv-text="{{nomeUsuario}}"/></source><target state="new">Olá <x id="INTERPOLATION" equiv-text="{{nomeUsuario}}"/></target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">34</context></context-group></trans-unit><trans-unit id="dashboard:user-company-unit" datatype="html">
        <source>
					Para começar, selecione abaixo a
					<x id="START_TAG_STRONG" ctype="x-strong" equiv-text="&lt;strong>"/>unidade<x id="CLOSE_TAG_STRONG" ctype="x-strong" equiv-text="&lt;/strong>"/> que quer acessar
				</source><target state="new">Para começar, selecione abaixo a <x id="START_TAG_STRONG" ctype="x-strong" equiv-text="&lt;strong>"/>unidade<x id="CLOSE_TAG_STRONG" ctype="x-strong" equiv-text="&lt;/strong>"/> que quer acessar</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">40</context></context-group></trans-unit><trans-unit id="login:dashboard-bloqueio-agendado" datatype="html">
        <source>Bloqueio Agendado</source><target state="new">Bloqueio Agendado</target>
        
      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">133</context></context-group></trans-unit><trans-unit id="dashboard:whatsapp-link" datatype="html">
        <source>
			Falar no WhatsApp
			<x id="START_TAG_LOGIN-CAT-ICON" ctype="x-login-cat-icon" equiv-text="&lt;login-cat-icon>"/><x id="CLOSE_TAG_LOGIN-CAT-ICON" ctype="x-login-cat-icon" equiv-text="&lt;/login-cat-icon>"/></source><target state="new"> Falar no WhatsApp <x id="START_TAG_LOGIN-CAT-ICON" ctype="x-login-cat-icon" equiv-text="&lt;login-cat-icon>"/><x id="CLOSE_TAG_LOGIN-CAT-ICON" ctype="x-login-cat-icon" equiv-text="&lt;/login-cat-icon>"/></target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">178</context></context-group></trans-unit><trans-unit id="dashboard:module-title" datatype="html">
        <source>Módulos disponíveis no Sistema Pacto</source><target state="new">Módulos disponíveis no Sistema Pacto</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">185</context></context-group></trans-unit><trans-unit id="login:dashboard-modulo-inativo" datatype="html">
        <source>Módulo inativo</source><target state="new">Módulo inativo</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">239</context></context-group></trans-unit><trans-unit id="login:dashboard-modulo-atito-sem-permissao

										" datatype="html">
        <source>Módulo inativo</source><target state="new">Módulo inativo</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context>
          <context context-type="linenumber">249</context>
        </context-group>
        <note priority="1" from="description">

											</note>
      </trans-unit><trans-unit id="dashboard:loading-modules" datatype="html">
        <source>
                            Carregando módulos...
                        </source><target state="new">
                            Carregando módulos...
                        </target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">259</context></context-group></trans-unit><trans-unit id="dashboard:selecionar-unidade" datatype="html">
        <source>
                            Selecione uma unidade para carregar os módulos.
                        </source><target state="new">
                            Selecione uma unidade para carregar os módulos.
                        </target>


      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">266</context></context-group><context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">283</context></context-group></trans-unit><trans-unit id="dashboard:loading-urls" datatype="html">
        <source>
                            Carregando urls...
                        </source><target state="new">
                            Carregando urls...
                        </target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">276</context></context-group></trans-unit><trans-unit id="dashboard:agenda-link" datatype="html">
        <source>Agenda</source><target state="new">Agenda</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">313</context></context-group></trans-unit><trans-unit id="dashboard:pessoa-link" datatype="html">
        <source>Pessoas</source><target state="new">Pessoas</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">337</context></context-group></trans-unit><trans-unit id="dashboard:config-link" datatype="html">
        <source>Configurações</source><target state="new">Configurações</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">362</context></context-group></trans-unit><trans-unit id="dashboard:modulo-zw" datatype="html">
        <source>Administrativo</source><target state="new">Administrativo</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">378</context></context-group></trans-unit><trans-unit id="dashboard:modulo-nzw" datatype="html">
        <source>ADM Beta</source><target state="new">ADM Beta</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">380</context></context-group></trans-unit><trans-unit id="dashboard:modulo-ntr" datatype="html">
        <source>Treino</source><target state="new">Treino</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">381</context></context-group></trans-unit><trans-unit id="dashboard:modulo-pay" datatype="html">
        <source>PactoPay</source><target state="new">Pacto Pay</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">382</context></context-group></trans-unit><trans-unit id="dashboard:modulo-fin" datatype="html">
        <source>Financeiro</source><target state="new">Financeiro</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">383</context></context-group></trans-unit><trans-unit id="dashboard:modulo-nav" datatype="html">
        <source>Avaliação</source><target state="new">Avaliação</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">384</context></context-group></trans-unit><trans-unit id="dashboard:modulo-ncr" datatype="html">
        <source>Cross</source><target state="new">Cross</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">385</context></context-group></trans-unit><trans-unit id="dashboard:modulo-crm" datatype="html">
        <source>CRM</source><target state="new">CRM</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">386</context></context-group></trans-unit><trans-unit id="dashboard:modulo-grd" datatype="html">
        <source>Graduação</source><target state="new">Graduação</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">387</context></context-group></trans-unit><trans-unit id="dashboard:modulo-gor" datatype="html">
        <source>Game of Results</source><target state="new">Game of Results</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">389</context></context-group></trans-unit><trans-unit id="dashboard:modulo-nf" datatype="html">
        <source>Nota Fiscal</source><target state="new">Nota Fiscal</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">391</context></context-group></trans-unit><trans-unit id="dashboard:modulo-ccl" datatype="html">
        <source>Canal do Cliente</source><target state="new">Canal do Cliente</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">393</context></context-group></trans-unit><trans-unit id="dashboard:error-load-url" datatype="html">
        <source>Ocorreu um erro ao carregar as urls.</source><target state="new">Ocorreu um erro ao carregar as urls.</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">396</context></context-group></trans-unit><trans-unit id="dashboard:error-load-warnings" datatype="html">
        <source>Ocorreu um erro ao carregar os avisos do sistema.</source><target state="new">Ocorreu um erro ao carregar os avisos do sistema.</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">399</context></context-group></trans-unit><trans-unit id="dashboard:info-sistema-suspenso-admin" datatype="html">
        <source>
		O acesso aos módulos dos sistema Pacto está completamente bloqueado.
		Entre em contato com a Pacto para realizar a quitação de suas pendências
		financeiras.
	</source><target state="new">
        O acesso aos módulos dos sistema Pacto está completamente bloqueado. Entre em contato com a Pacto para realizar a quitação de suas pendências financeiras.
    </target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">405</context></context-group></trans-unit><trans-unit id="dashboard:info-sistema-bloqueado-admin" datatype="html">
        <source>
		Seu sistema Pacto está bloqueado e apenas operações básicas estarão
		disponíveis. Acesse o módulo Administrativo e regularize suas pendências
		financeiras.
	</source><target state="new">
        Seu sistema Pacto está bloqueado e apenas operações básicas estarão disponíveis. Acesse o módulo Administrativo e regularize suas pendências financeiras.
    </target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">413</context></context-group></trans-unit><trans-unit id="dashboard:info-sistema-parcial-admin" datatype="html">
        <source>
		Seu sistema Pacto está bloqueado parcialmente. Realize o pagamento de
		suas pendências financeiras para continuar utilizando todas as
		funcionalidades.
	</source><target state="new">
        Seu sistema Pacto está bloqueado parcialmente. Realize o pagamento de suas pendências financeiras para continuar utilizando todas as funcionalidades.
    </target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">421</context></context-group></trans-unit><trans-unit id="dashboard:info-sistema-suspenso" datatype="html">
        <source>
		O acesso aos módulos do sistema Pacto está completamente bloqueado. Para
		extrair o máximo das nossas funcionalidades, entre em contato com o
		administrador para quitar suas pendências financeiras.
	</source><target state="new">
        O acesso aos módulos do sistema Pacto está completamente bloqueado. Para extrair o máximo das nossas funcionalidades, entre em contato com o administrador para quitar suas pendências financeiras.
    </target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">429</context></context-group></trans-unit><trans-unit id="dashboard:info-sistema-bloqueado" datatype="html">
        <source>
		O sistema Pacto está bloqueado devido a pendências financeiras. Para
		extrair o máximo das nossas funcionalidades, faça login com o perfil de
		administrador para realizar o desbloqueio parcial.
	</source><target state="new">
        O sistema Pacto está bloqueado devido a pendências financeiras. Para extrair o máximo das nossas funcionalidades, faça login com o perfil de administrador para realizar o desbloqueio parcial.
    </target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">437</context></context-group></trans-unit><trans-unit id="dashboard:info-sistema-parcial" datatype="html">
        <source>
		O sistema Pacto está funcionando de forma parcial durante os próximos
		<x id="INTERPOLATION" equiv-text="{{ infoBloqueio?.diasBloqueio }}"/> dias. Para extrair o máximo das nossas
		funcionalidades, faça login com o perfil de administrador para quitar
		suas pendências financeiras.
	</source><target state="new">
        O sistema Pacto está funcionando de forma parcial durante os próximos <x id="INTERPOLATION" equiv-text="{{infoBloqueio.diasBloqueio}}"/> dias. Para extrair o máximo das nossas funcionalidades, faça login com o perfil de administrador para quitar suas pendências financeiras.
    </target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">445</context></context-group></trans-unit><trans-unit id="dashboard:info-sistema-agendado" datatype="html">
        <source>Dentro de instantes seu Sistema Pacto será parcialmente
					bloqueado devido a pendências financeiras. Acesse o canal do
					cliente e regularize essa situação.</source><target state="new">
        Dentro de instantes seu Sistema Pacto será parcialmente bloqueado devido a pendências financeiras. Acesse o canal do cliente e regularize essa situação.
    </target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">146</context></context-group></trans-unit><trans-unit id="dashboard:info-sistema-agendado-dias" datatype="html">
        <source>Faltam apenas
					<x id="START_TAG_STRONG" ctype="x-strong" equiv-text="&lt;strong>"/><x id="INTERPOLATION" equiv-text="{{ infoBloqueio.diasBloqueio }}"/>
						<x id="INTERPOLATION_1" equiv-text="{{
							infoBloqueio.diasBloqueio > 1
								? traducoes.getLabel('dias')
								: traducoes.getLabel('dia')
						}}"/><x id="CLOSE_TAG_STRONG" ctype="x-strong" equiv-text="&lt;/strong>"/>
					para que seu Sistema Pacto seja parcialmente bloqueado
					devido a pendências financeiras. Acesse o canal do cliente e
					regularize essa situação.</source><target state="new">
        Faltam apenas <x id="INTERPOLATION" equiv-text="{{infoBloqueio.diasBloqueio}}"/> para que seu Sistema Pacto seja parcialmente bloqueado devido a pendências financeiras. Acesse o canal do cliente e regularize essa situação.
    </target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">156</context></context-group></trans-unit><trans-unit id="dashboard:title-sistema-suspenso" datatype="html">
        <source>Sistema suspenso</source><target state="new">Sistema suspenso</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">453</context></context-group></trans-unit><trans-unit id="dashboard:title-sistema-bloqueado" datatype="html">
        <source>Sistema bloqueado</source><target state="new">Sistema bloqueado</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">456</context></context-group></trans-unit><trans-unit id="dashboard:title-sistema-parcial" datatype="html">
        <source>Bloqueio parcial</source><target state="new">Bloqueio parcial</target>

      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">459</context></context-group></trans-unit><trans-unit id="dashboard:dias" datatype="html">
        <source>dias</source><target state="new">dias</target>
        
      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">462</context></context-group></trans-unit><trans-unit id="dashboard:dia" datatype="html">
        <source>dia</source><target state="new">dia</target>
        
      <context-group purpose="location"><context context-type="sourcefile">src/app/components/dashboard/dashboard.component.html</context><context context-type="linenumber">463</context></context-group></trans-unit><trans-unit id="login:try-again" datatype="html">
        <source>Tentar novamente!</source><target state="new">Tentar novamente!</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/activate-account/activate-account.component.html</context>
          <context context-type="linenumber">4</context>
        </context-group>
      </trans-unit><trans-unit id="login:goback-login" datatype="html">
        <source>Voltar para o login</source><target state="new">Voltar para o login</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/activate-account/activate-account.component.html</context>
          <context context-type="linenumber">5</context>
        </context-group>
      </trans-unit><trans-unit id="login:activate-account-load" datatype="html">
        <source>Aguarde enquanto ativo sua conta!</source><target state="new">Aguarde enquanto ativo sua conta!</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/activate-account/activate-account.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
      </trans-unit><trans-unit id="login:error-trying-activate" datatype="html">
        <source>Ocorreu um erro ao tentar ativar sua conta.</source><target state="new">Ocorreu um erro ao tentar ativar sua conta.</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/activate-account/activate-account.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
      </trans-unit><trans-unit id="login:account-activated" datatype="html">
        <source>Sua conta foi ativada com sucesso!</source><target state="new">Sua conta foi ativada com sucesso!</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/activate-account/activate-account.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
      </trans-unit><trans-unit id="login:invalid-token" datatype="html">
        <source>Erro ao validar o token!</source><target state="new">Erro ao validar o token!</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/activate-account/activate-account.component.html</context>
          <context context-type="linenumber">18</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-email-title" datatype="html">
        <source>Altere seu e-mail</source><target state="new">Altere seu e-mail</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-email/change-email.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-email-hint" datatype="html">
        <source>
    <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span>"/>
        Estamos quase lá!<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br/>"/>
        Informe abaixo seu novo e-mail.
    <x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span>"/>
</source><target state="new">
    <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span>"/>
        Estamos quase lá!<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br/>"/>
        Informe abaixo seu novo e-mail.
    <x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span>"/>
</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-email/change-email.component.html</context>
          <context context-type="linenumber">4</context>
        </context-group>
      </trans-unit><trans-unit id="login:label-novo-email" datatype="html">
        <source>Nova e-mail</source><target state="new">Nova e-mail</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-email/change-email.component.html</context>
          <context context-type="linenumber">12</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-email" datatype="html">
        <source>Digite seu novo e-mail</source><target state="new">Digite seu novo e-mail</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-email/change-email.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-email-required" datatype="html">
        <source>Digite o novo e-mail!</source><target state="new">Digite o novo e-mail!</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-email/change-email.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-email-invalid" datatype="html">
        <source>Insira um e-mail válido!</source><target state="new">Insira um e-mail válido!</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-email/change-email.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
      </trans-unit><trans-unit id="login:change-email-submit" datatype="html">
        <source>Alterar e-mail
    </source><target state="new">Alterar e-mail
    </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-email/change-email.component.html</context>
          <context context-type="linenumber">21</context>
        </context-group>
      </trans-unit><trans-unit id="login:email-changed-success" datatype="html">
        <source>E-mail alterado para <x id="INTERPOLATION" equiv-text="{{form?.get('email')?.value}}"/> com sucesso!</source><target state="new">E-mail alterado para <x id="INTERPOLATION" equiv-text="{{form?.get('email')?.value}}"/> com sucesso!</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-email/change-email.component.html</context>
          <context context-type="linenumber">30</context>
        </context-group>
      </trans-unit><trans-unit id="login:email-change-unkown-error" datatype="html">
        <source>Ocorreu um erro desconhecido ao tentar alterar o e-mail. Contate o suporte!</source><target state="new">Ocorreu um erro desconhecido ao tentar alterar o e-mail. Contate o suporte!</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-email/change-email.component.html</context>
          <context context-type="linenumber">31</context>
        </context-group>
      </trans-unit><trans-unit id="login:email-token-unkown-error" datatype="html">
        <source>Erro desconhecido ao validar o token para troca de e-mail. Contate o suporte!</source><target state="new">Erro desconhecido ao validar o token para troca de e-mail. Contate o suporte!</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-email/change-email.component.html</context>
          <context context-type="linenumber">32</context>
        </context-group>
      </trans-unit><trans-unit id="login:email-invalid-token" datatype="html">
        <source>Token inválido@</source><target state="new">Token inválido@</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/change-email/change-email.component.html</context>
          <context context-type="linenumber">33</context>
        </context-group>
      </trans-unit><trans-unit id="login:choose-how-to-login" datatype="html">
        <source>Como deseja continuar?</source><target state="new">Como deseja continuar?</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/choose-how-login/choose-how-login.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
      </trans-unit><trans-unit id="login:email" datatype="html">
        <source>E-mail</source><target state="new">E-mail</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/choose-how-login/choose-how-login.component.html</context>
          <context context-type="linenumber">5</context>
        </context-group>
      </trans-unit><trans-unit id="login:celular" datatype="html">
        <source>Celular</source><target state="new">Celular</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/choose-how-login/choose-how-login.component.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
      </trans-unit>
    </body>
  </file>
</xliff>
