import { Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";

const categories = [
	"06h",
	"07h",
	"08h",
	"09h",
	"10h",
	"11h",
	"12h",
	"13h",
	"14h",
	"15h",
	"16h",
	"17h",
	"18h",
	"19h",
	"20h",
	"21h",
	"22h",
	"23h",
	"00h",
];

const daysOfWeek = ["Sáb", "Seg", "Ter", "Qua", "Qui", "Sex", "Dom"];

@Component({
	selector: "pacto-adapters",
	templateUrl: "./adapters.component.html",
	styleUrls: ["./adapters.component.scss"],
})
export class AdaptersComponent implements OnInit {
	// totalItems: number = 30; // Suponha que você tenha um total de 1000 itens
	// pageSize: number = 5; // Tamanho padr<PERSON> da página
	// currentPage: number = 1; // Página atual, inicializada como 0
	// form: FormGroup;

	data = [
		{
			value: 100,
			dayFull: "Sábado",
			day: "Sáb",
			mediaAcesso: 40,
			mediaAcessoQuadrado: 5,
			gympass: 2,
			totalpass: 5,
			freepass: 5,
			diaria: 0,
			hora: "23",
		},
		{
			value: 50,
			dayFull: "Sábado",
			day: "Sáb",
			mediaAcesso: 30,
			mediaAcessoQuadrado: 5,
			gympass: 2,
			totalpass: 5,
			freepass: 5,
			diaria: 0,
			hora: "06",
		},
		{
			value: 20,
			dayFull: "Sábado",
			day: "Sáb",
			mediaAcesso: 20,
			mediaAcessoQuadrado: 5,
			gympass: 2,
			totalpass: 5,
			freepass: 5,
			diaria: 0,
			hora: "08",
		},
		{
			value: 10,
			dayFull: "Sábado",
			day: "Sáb",
			mediaAcesso: 10,
			mediaAcessoQuadrado: 5,
			gympass: 2,
			totalpass: 5,
			freepass: 5,
			diaria: 0,
			hora: "07",
		},

		{
			value: 100,
			dayFull: "Sexta",
			day: "Sex",
			mediaAcesso: 2,
			mediaAcessoQuadrado: 6,
			gympass: 4,
			totalpass: 2,
			freepass: 3,
			diaria: 1,
			hora: "08",
		},
		{
			value: 50,
			dayFull: "Sexta",
			day: "Sex",
			mediaAcesso: 4,
			mediaAcessoQuadrado: 6,
			gympass: 4,
			totalpass: 2,
			freepass: 3,
			diaria: 1,
			hora: "06",
		},

		{
			value: 50,
			dayFull: "Terça",
			day: "Ter",
			mediaAcesso: 4,
			mediaAcessoQuadrado: 6,
			gympass: 4,
			totalpass: 2,
			freepass: 3,
			diaria: 1,
			hora: "06",
		},
		{
			value: 50,
			dayFull: "Terça",
			day: "Ter",
			mediaAcesso: 4,
			mediaAcessoQuadrado: 6,
			gympass: 4,
			totalpass: 2,
			freepass: 3,
			diaria: 1,
			hora: "06",
		},

		{
			value: 50,
			dayFull: "Domingo",
			day: "Dom",
			mediaAcesso: 5,
			mediaAcessoQuadrado: 8,
			gympass: 6,
			totalpass: 4,
			freepass: 3,
			diaria: 1,
			hora: "07",
		},
		{
			value: 100,
			dayFull: "Domingo",
			day: "Dom",
			mediaAcesso: 10,
			mediaAcessoQuadrado: 8,
			gympass: 6,
			totalpass: 4,
			freepass: 3,
			diaria: 1,
			hora: "08",
		},
		{
			value: 100,
			dayFull: "Domingo",
			day: "Dom",
			mediaAcesso: 20,
			mediaAcessoQuadrado: 8,
			gympass: 6,
			totalpass: 4,
			freepass: 3,
			diaria: 1,
			hora: "06",
		},
	];

	chartOptions = {
		series: this.generateSeries(),
		xaxis: {
			type: "category",
			categories: categories,
		},
	};

	constructor() {} //private fb: FormBuilder,

	ngOnInit() {
		this.organizeDataByDayAndHour();
		this.updateData();
		//   this.form = this.fb.group({
		//     arquivo: this.fb.group({
		//       dados: [null],
		//       nome: [null]
		//     }),
		//     fotoKeyUrlFull: ['https://cdn1.pactorian.net/3ec289516f552b0b47ce777661bbb6b5/97jxGKuba0*PGeIPqAkibQ==/9lLvqI6KAmgh9Z6yGp7pcQ==.jpg']
		//   });
	}

	// onPageChange(pageIndex: number): void {
	//   this.currentPage = pageIndex;
	// }

	// onPageSizeChange(pageSize: number): void {
	//   this.pageSize = pageSize;
	// }
	organizeDataByDayAndHour() {
		const dataOrganizada = this.data.sort((a, b) => {
			const diaComparacao =
				daysOfWeek.indexOf(a.day) - daysOfWeek.indexOf(b.day);
			if (diaComparacao === 0) {
				return Number(a.hora) - Number(b.hora);
			}
			return diaComparacao;
		});
	}

	generateSeries() {
		return daysOfWeek.map((day) => ({
			name: day,
			data: this.generateInitialData(),
		}));
	}

	generateTooltip({
		series,
		seriesIndex,
		dataPointIndex,
		customTooltipData,
		x,
	}) {
		const row = daysOfWeek[seriesIndex];
		const colunn = categories[dataPointIndex];
		const filteredData = customTooltipData.filter((d) => d.day === row);
		const dataPoint = filteredData.filter(
			(v) =>
				v.value === series[seriesIndex][dataPointIndex] &&
				`${v.hora}h` === colunn
		)[0];
		if (!dataPoint) {
			return "";
		}
		return `<div style="margin: 8px 12px; display: flex; flex-direction: column;" >
                <span style="font-family: Nunito Sans; font-size: 14px; font-weight: 400; line-height: 17.5px; text-align: left;"> ${dataPoint.dayFull}  ${dataPoint.hora} </span>                
                <span>_______________________________________________________________________________</span>
                <span style="font-family: Nunito Sans; font-size: 12px; font-weight: 400; line-height: 16px; text-align: left; margin: 12px 0;"> Média de acesso: ${dataPoint.mediaAcesso} alunos </span> 
                <span style="font-family: Nunito Sans; font-size: 12px; font-weight: 400; line-height: 16px; text-align: left;"> Média de alunos ativos por metro quadrados: ${dataPoint.mediaAcessoQuadrado} alunos </span> 
                <span style="font-family: Nunito Sans; font-size: 12px; font-weight: 400; line-height: 16px; text-align: left;margin: 12px 0;"> Gympass: ${dataPoint.gympass} alunos </span>
                <span style="font-family: Nunito Sans; font-size: 12px; font-weight: 400; line-height: 16px; text-align: left;"> TotalPass: ${dataPoint.totalpass} alunos </span>
                <span style="font-family: Nunito Sans; font-size: 12px; font-weight: 400; line-height: 16px; text-align: left; margin: 12px 0;"> FreePass: ${dataPoint.freepass} alunos </span>
                <span style="font-family: Nunito Sans; font-size: 12px; font-weight: 400; line-height: 16px; text-align: left; margin-bottom: 12px"> Diária: ${dataPoint.diaria} alunos </span>
              </div>`;
	}

	generateInitialData() {
		return categories.map((category) => ({ x: category, y: 0 }));
	}

	updateData() {
		this.data.forEach((item) => {
			const index = categories.indexOf(`${item.hora}h`);
			const indexDay = daysOfWeek.indexOf(item.day);
			this.chartOptions.series[indexDay].data[index].y = item.value;
		});
	}
}
