import { CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID, NgModule } from "@angular/core";
import { BrowserModule } from "@angular/platform-browser";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { HttpClientModule } from "@angular/common/http";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { AppRoutingModule } from "./app-routing.module";

import { RouterModule } from "@angular/router";
import { UiModule } from "projects/ui/src/lib/ui.module";
import { AppComponent } from "./app.component";
import { DatepickerComponent } from "./datepicker/datepicker.component";
import { MockComponentComponent } from "./mock-component/mock-component.component";
import { NotificacaoApiComponent } from "./adapters/notificacao-api/notificacao-api/notificacao-api.component";
import {
	NotificacaoApiConfigProviderBaseService,
	NotificacaoApiModule,
} from "notificacao-api";
import { NotificacaoApiConfigProvider } from "./adapters/api-config-providers/notificacao-api-config-provider";
import { SnotifyService, ToastDefaults } from "ng-snotify";
import { Ds3SandboxComponent } from "./ds3-sandbox/ds3-sandbox.component";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import ptBr from "@angular/common/locales/pt";
import { registerLocaleData } from "@angular/common";

registerLocaleData(ptBr);
import { TipografiaComponent } from "./tipografia/tipografia.component";
import { Ds3ItensComponent } from "./ds3-sandbox/childrens/ds3-itens/ds3-itens.component";
import { Ds3ItemComponent } from "./ds3-sandbox/childrens/ds3-item/ds3-item.component";
import { Ds3ItemEditComponent } from "./ds3-sandbox/childrens/ds3-item-edit/ds3-item-edit.component";
import { Ds3TestePageComponent } from "./ds3-sandbox/childrens/ds3-teste-page/ds3-teste-page.component";
import { Ds3Module } from "projects/ui/src/lib/ds3/ds3.module";
import { AdaptersComponent } from "./adapters/adapters.component";
import { NgxCurrencyModule } from "ngx-currency";
import { MatTooltipModule } from "@angular/material/tooltip";

@NgModule({
	declarations: [
		AppComponent,
		DatepickerComponent,
		TipografiaComponent,
		MockComponentComponent,
		NotificacaoApiComponent,
		AdaptersComponent,
		Ds3SandboxComponent,
		Ds3ItensComponent,
		Ds3ItemComponent,
		Ds3ItemEditComponent,
		Ds3TestePageComponent,
	],
	imports: [
		AppRoutingModule,
		RouterModule.forRoot([]),
		BrowserModule,
		BrowserAnimationsModule,
		HttpClientModule,
		NgbModule,
		UiModule,
		ReactiveFormsModule,
		NotificacaoApiModule,
		FormsModule,
		ReactiveFormsModule,
		NgxCurrencyModule,
		MatTooltipModule,
	],
	providers: [
		{ provide: LOCALE_ID, useValue: "pt-BR" },
		{
			provide: NotificacaoApiConfigProviderBaseService,
			useClass: NotificacaoApiConfigProvider,
		},
		SnotifyService,
		{ provide: "SnotifyToastConfig", useValue: ToastDefaults },
	],
	bootstrap: [AppComponent],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppModule {}
