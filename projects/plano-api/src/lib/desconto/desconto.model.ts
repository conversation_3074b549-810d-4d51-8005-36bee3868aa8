import { Empresa } from "../base/empresa.model";
import { JustificativaOperacao } from "../justificativa-operacao/justificativa-operacao.model";

export class Desconto {
	codigo: number;
	descricao: string;
	tipoProduto: string;
	tipoDesconto: string;
	valor: number;
	ativo: boolean;
	aplicarEmpresas: boolean;
	descontoEmpresas: Array<DescontoEmpresa>;
	descontoRenovacoes: Array<DescontoRenovacao>;
}

export class DescontoEmpresa {
	codigo: number;
	desconto?: Desconto;
	empresa: Empresa;
}

export class DescontoRenovacao {
	codigo: number;
	desconto?: Desconto;
	tipoIntervalo: string;
	intervaloDe: number;
	intervaloAte: number;
	tipoDesconto: string;
	valor: number;
	justificativaOperacao: JustificativaOperacao;
}

export enum TipoIntervalo {
	antecipado = "AN",
	atrasado = "AT",
}

export const tipoIntervaloText = (tipoIntervalo) => {
	switch (TipoIntervalo) {
		case tipoIntervalo.antecipado:
			return "antecipado";
		case tipoIntervalo.atrasado:
			return "atrasado";
		default:
			return undefined;
	}
};

export const getTipoDescontoLabelByKey = (traducaoId: string, traducao) => {
	return traducao.getLabel(traducaoId);
};

export const getTipoDescontoText = (traducao) => {
	return new Array<{ id: string; label: string }>(
		{ id: "PE", label: traducao.getLabel("PE") },
		{ id: "VA", label: traducao.getLabel("VA") },
		{ id: "BO", label: traducao.getLabel("BO") }
	);
};
