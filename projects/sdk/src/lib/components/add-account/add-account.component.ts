import { Component, Input, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";

import { of } from "rxjs";
import { switchMap } from "rxjs/operators";

import { ConfigCache } from "./model";
import { SessionService } from "../../services/session.service";
import { PlataformaModulo } from "../../services/models/client-discovery.model";
import { LocalStorageSessionService } from "../../services/local-storage-session.service";
import { LoginUrlQueries } from "../../services/models/client-model";

@Component({
	selector: "sdk-add-account",
	templateUrl: "./add-account.component.html",
	styleUrls: ["./add-account.component.scss"],
})
export class AddAccountComponent implements OnInit {
	loading = true;
	fail = false;

	@Input() configuracoesService: ConfigCache;

	constructor(
		private router: Router,
		private route: ActivatedRoute,
		private localStorageSession: LocalStorageSessionService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.localStorageSession.clear();
		const query: LoginUrlQueries = this.route.snapshot
			.queryParams as LoginUrlQueries;
		this.localStorageSession.setLocalStorageParams(query);

		if (query.usuarioOamd) {
			this.sessionService.usuarioOamd = query.usuarioOamd;
		}

		this.sessionService
			.loadSession(query)
			.pipe(
				switchMap((success) => {
					if (success) {
						this.sessionService.montarChatMovDesk();
						return this.configuracoesService.loadConfigCache();
					} else {
						return of(false);
					}
				})
			)
			.subscribe((success) => {
				this.loading = false;
				this.fail = !success;
				if (success) {
					if (query.redirect) {
						this.router.navigateByUrl(query.redirect);
					} else {
						this.redirectToModule(query.moduleId as any);
					}
				}
			});
	}

	private redirectToModule(target?: PlataformaModulo) {
		const finalTarget = this.getTargetModule(target);
		if (finalTarget === PlataformaModulo.NZW) {
			this.router.navigate(["adm"]);
		} else if (finalTarget === PlataformaModulo.NTR) {
			this.router.navigate(["treino"]);
		} else if (finalTarget === PlataformaModulo.NCR) {
			this.router.navigate(["crossfit"]);
		} else if (finalTarget === PlataformaModulo.NAV) {
			this.router.navigate(["avaliacao"]);
		} else if (finalTarget === PlataformaModulo.GRD) {
			this.router.navigate(["graduacao"]);
		} else if (finalTarget === PlataformaModulo.PAY) {
			this.router.navigate(["pactopay"]);
		} else if (finalTarget === PlataformaModulo.NCRM) {
			this.router.navigate(["crm"]);
		} else if (
			finalTarget === PlataformaModulo.AGENDA ||
			finalTarget === PlataformaModulo.AGE ||
			finalTarget === PlataformaModulo.AGN
		) {
			this.router.navigate(["agenda"]);
		} else {
			this.router.navigate(["/home"]);
		}
	}

	private getTargetModule(target?: PlataformaModulo): PlataformaModulo {
		const habilitados = this.sessionService.modulosHabilitados;
		if (target && habilitados.includes(target)) {
			return target;
		} else if (
			habilitados.includes(PlataformaModulo.NTR) &&
			(target === PlataformaModulo.AGENDA ||
				target === PlataformaModulo.AGN ||
				target === PlataformaModulo.AGE)
		) {
			return target;
		} else {
			return null;
		}
	}
}
